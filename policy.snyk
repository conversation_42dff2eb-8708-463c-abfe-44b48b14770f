# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.25.0
#language-settings: # Language settings are optional
#  python: "3.7"
#ignore:
#  SNYK-VUL:
#    - mongodb > mongodb-core > bson: # Path to ignore (use '*' with quotes for all paths)
#        reason: None given
#        expires: '2020-06-19T20:36:54.553Z' (expire is optional, remove the line if expiry is not wanted)
# patches apply the minimum changes required to fix a vulnerability
#patch:
#  'npm:hawk:20160119':
#    - tap > codecov.io > request > hawk:
#        patched: '2020-01-20T14:26:34.404Z'
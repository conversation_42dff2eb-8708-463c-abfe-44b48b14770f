clone:
  depth: full
definitions:
  services:
    docker:
      image:
        name: docker:dind
        username: $DOCKER_HUB_USERNAME
        password: $DOCKER_HUB_PASSWORD
        email: $DOCKER_HUB_EMAIL
      memory: 1024
  steps:
    - step: &prep-env
        name: Prepare Environment Vars
        oidc: true
        size: 1x
        image:
          name: amazon/aws-cli:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          # Export env vars from file
          - set -a && . ./pipeline.env && set +a
          # Set AWSCLI env vars
          - export AWS_WEB_IDENTITY_TOKEN_FILE=$(pwd)/web-identity-token
          - echo $BITBUCKET_STEP_OIDC_TOKEN > $(pwd)/web-identity-token
          # Create Canonical Version
          - export timestamp=$(date +%s | tr -d '[:space:]')
          - export escaped_branch=$(echo $BITBUCKET_<PERSON>ANCH | sed "s=/=_=g" | tr -d '[:space:]')
          - echo export BRANCH_NAME=$escaped_branch >> build.env
          - echo export BUILD_IMAGE_TAG="${escaped_branch}-${BITBUCKET_COMMIT:0:8}-${timestamp}" >> build.env
          # Find base branch for use in image building
          - if [[ $escaped_branch == $STG_BRANCH ]]; then echo export BASE_BRANCH=$STG_BRANCH >> build.env; elif [[ $escaped_branch == $PRD_BRANCH ]]; then echo export BASE_BRANCH=$PRD_BRANCH >> build.env; else echo export BASE_BRANCH=$DEV_BRANCH >> build.env; fi
          # Get Snyk Creds
          - echo export SNYK_TOKEN=$(aws secretsmanager get-secret-value --secret-id devTools/snyk_token --query SecretString --output text) >> build.env
          # Get AWS Creds
          - echo export AWS_TOKEN=$(aws ecr get-login-password --region us-east-1) >> build.env
          # Get ACR Creds
          - echo export ACR_DV_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_dv_token --query SecretString --output text) >> build.env
          - echo export ACR_DV_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_dv_2_token --query SecretString --output text) >> build.env
          - echo export ACR_UA_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_ua_token --query SecretString --output text) >> build.env
          - echo export ACR_UA_2_TOKEN=$(aws secretsmanager get-secret-value --secret-id platform/acr_ua_2_token --query SecretString --output text) >> build.env
          # Code Artifact Creds
          - export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner 411985166407 --region us-east-1 --query authorizationToken --output text)
          # Fetch Maven settings file
          - touch mvn_settings.xml
          - echo $(aws ssm get-parameter --name $MVN_SETTINGS_SSM_PARAM --with-decryption --query Parameter.Value --output text) > settings.xml
          # Use Python (sed "s//g" variable expansion doesn't work) to expand and inject token variable
          # NOTE: the input and output files must be different. Using the same file for both results in a blank file.
          - python3 -c 'import os,sys; sys.stdout.write(os.path.expandvars(sys.stdin.read()))' < settings.xml > mvn_settings.xml
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - build.env
          - mvn_settings.xml
    - step: &sonarqube
        name: SonarQube Quality Gate
        oidc: true
        size: 1x
        image:
          name: maven:3.9-amazoncorretto-21
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - export TESTCONTAINERS_RYUK_DISABLED=true
          - mvn -s mvn_settings.xml verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - maven
    - step: &snyk-code
        name: Synk - Code Test
        oidc: true
        size: 1x
        image:
          name: snyk/snyk:maven
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - snyk test --policy-path=policy.snyk --fail-on=all --severity-threshold=high --print-deps --color --json-file-output=output.json
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - output.json
    - step: &upload-snyk-code-report
        name: Synk - Upload Code Report
        oidc: true
        size: 1x
        image:
          name: bitbucketpipelines/bitbucket-upload-file:0.7.3
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - export BITBUCKET_ACCESS_TOKEN=$BITBUCKET_TOKEN
          - export FILENAME=snyk-code-$BUILD_IMAGE_TAG.json
          - cp output.json $FILENAME
          - python3 /pipe.py
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
    - step: &build-image
        name: Build Image
        oidc: true
        size: 1x
        image:
          name: maven:3.9-amazoncorretto-21
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - docker pull $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BASE_BRANCH-latest || true
          - mvn -s mvn_settings.xml install -DskipTests -Ddocker.image.repo=$BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME -Ddocker.image.tag=$BASE_BRANCH-latest
          - docker save --output built.tar $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BASE_BRANCH-latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
        caches:
          - maven
        artifacts:
          - built.tar
          - build.env
    - step: &snyk-image
        name: Synk - Image Test
        oidc: true
        size: 1x
        image:
          name: snyk/snyk:docker
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - snyk container test docker-archive:built.tar --policy-path=policy.snyk --fail-on=all --severity-threshold=critical --print-deps --color --json-file-output=output.json
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        artifacts:
          - output.json
    - step: &upload-snyk-image-report
        name: Synk - Upload Image Test Report
        oidc: true
        size: 1x
        image:
          name: bitbucketpipelines/bitbucket-upload-file:0.7.3
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - export BITBUCKET_ACCESS_TOKEN=$BITBUCKET_TOKEN
          - export FILENAME=snyk-image-$BUILD_IMAGE_TAG.json
          - cp output.json $FILENAME
          - python3 /pipe.py
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
    - step: &push-ecr
        name: Push to ECR
        oidc: true
        size: 1x
        image:
          name: amazon/aws-cli:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-development
        name: Push to ACR - Development
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_DV_TOKEN | docker login -u $AZURE_USER_DEV --password-stdin $AZURE_REPO_DEV
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest $AZURE_REPO_DEV/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO_DEV/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $AZURE_REPO_DEV/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO_DEV/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO_DEV/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-development-2
        name: Push to ACR - Development 2
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_DV_2_TOKEN | docker login -u $AZURE_USER_DEV2 --password-stdin $AZURE_REPO_DEV2
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest $AZURE_REPO_DEV2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO_DEV2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker tag $AZURE_REPO_DEV2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG $AZURE_REPO_DEV2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO_DEV2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-staging
        name: Push to ACR - Staging
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_UA_TOKEN | docker login -u $AZURE_USER_STG --password-stdin $AZURE_REPO_STG
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest $AZURE_REPO_STG/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO_STG/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &push-acr-2-staging
        name: Push to ACR - Staging
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - docker load --input built.tar
          - echo $ACR_UA_2_TOKEN | docker login -u $AZURE_USER_STG2 --password-stdin $AZURE_REPO_STG2
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BRANCH_NAME-latest $AZURE_REPO_STG2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
          - docker push $AZURE_REPO_STG2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &apc-retag
        name: Re-tag existing image and push to ACR Staging
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - echo $AWS_TOKEN | docker login -u AWS --password-stdin $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME
          - echo $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG
          - docker pull $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG
          - echo $ACR_UA_TOKEN | docker login -u $AZURE_USER_STG --password-stdin $AZURE_REPO_STG
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG $AZURE_REPO_STG/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO_STG/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - echo $ACR_UA_2_TOKEN | docker login -u $AZURE_USER_STG2 --password-stdin $AZURE_REPO_STG2
          - docker tag $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$EXISTING_TAG $AZURE_REPO_STG2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
          - docker push $AZURE_REPO_STG2/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:latest
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker
    - step: &finish
        name: Notify Teams of Success
        oidc: true
        size: 1x
        image:
          name: docker:latest
          username: $DOCKER_HUB_USERNAME
          password: $DOCKER_HUB_PASSWORD
          email: $DOCKER_HUB_EMAIL
        runs-on:
          - self.hosted
          - linux
        script:
          - set -a && . pipeline.env && . build.env && set +a
          - echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin
          - docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Succeeded" "${BITBUCKET_REPO_FULL_NAME} -- ${BITBUCKET_BRANCH} -- $BASE_ECR_URL/$BUILD_IMAGE_ORG/$BUILD_IMAGE_NAME:$BUILD_IMAGE_TAG"
        after-script:
          - if [[ BITBUCKET_EXIT_CODE -ne 0 ]]; then echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin && docker run --rm esdsdockerserviceaccount/notify-teams:latest notify ${TEAMS_WEBHOOK} "Build Failed" "${BITBUCKET_REPO_FULL_NAME} - ${BITBUCKET_BRANCH}"; fi
        services:
          - docker

pipelines:
  custom:
    apc-retag-for-staging:
      - variables:
          - name: EXISTING_TAG
            description: "Image tag that will be retagged as 'latest' and pushed to APC staging environment"
      - step: *prep-env
      - step: *apc-retag
  default:
    - step: *prep-env
    - step: *sonarqube
    - step: *snyk-code
    - step: *finish
  branches:
    '{development}':
      - step: *prep-env
      - step: *sonarqube
      - step: *snyk-code
      - step: *upload-snyk-code-report
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-development
      - step: *push-acr-development-2
      - step: *finish
    '{staging}':
      - step: *prep-env
      - step: *sonarqube
      - step: *snyk-code
      - step: *upload-snyk-code-report
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *push-acr-staging
      - step: *push-acr-2-staging
      - step: *finish
    '{main}':
      - step: *prep-env
      - step: *sonarqube
      - step: *snyk-code
      - step: *build-image
      - step: *snyk-image
      - step: *upload-snyk-image-report
      - step: *push-ecr
      - step: *finish
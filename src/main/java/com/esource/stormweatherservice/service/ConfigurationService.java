package com.esource.stormweatherservice.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.esource.stormweatherservice.model.Config;
import com.esource.stormweatherservice.model.Configurations;
import com.esource.stormweatherservice.repository.ConfigurationRepository;

@Service
public class ConfigurationService {

    @Autowired
    private ConfigurationRepository configurationRepository;

    public List<Configurations> getConfigurationsByTypeAndConfigType(String type, String configType) {
        try {
            List<Configurations> configurations = configurationRepository.findByTypeAndConfigType(type, configType);
            return configurations.stream()
                    .map(configuration -> {
                        List<Config> filteredConfigs = configuration.getConfigs().stream()
                                .filter(config -> config.getType().equals(configType))
                                .collect(Collectors.toList());
                        configuration.setConfigs(filteredConfigs);
                        return configuration;
                    })
                    .collect(Collectors.toList());
        } catch (Exception ex) {
            return List.of();
        }
    }

    public List<Configurations> getAllConfigurationsByType(String type) {
        try {
            return configurationRepository.findByType(type);
        } catch (Exception ex) {
            return List.of();
        }
    }
}

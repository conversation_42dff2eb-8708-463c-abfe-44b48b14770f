package com.esource.stormweatherservice.service;

import java.util.List;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.model.SystemWeather;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.repository.ResultSessionRepository;
import com.esource.stormweatherservice.repository.SystemWeatherDao;
import com.esource.stormweatherservice.repository.SystemWeatherRepository;
import com.esource.stormweatherservice.service.mapper.WeatherAttributeResultDocumentMapper;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@RequiredArgsConstructor
public class SystemWeatherService {
    private static final Logger log = LoggerFactory.getLogger(SystemWeatherService.class);
    private static final int SYSTEM_DAILY_PREDICTIONS_LIMIT = 5;
    private static final String DAILY_ZONE = "daily_zone";

    private final SystemWeatherRepository systemWeatherRepository;
    private final ResultSessionRepository resultSessionRepository;
    private final SystemWeatherDao systemWeatherDao;
    private final WeatherAttributeResultDocumentMapper weatherAttributeResultDocumentMapper;

    public List<WeatherAttributeResultDocument> getAllResultsBySession(@NonNull String session, String parentZone) {
        var sessionId = new ObjectId(session);
        List<SystemWeather> documents;

        if (isEmpty(parentZone)) {
            documents = systemWeatherRepository.findTop6BySessionOrderByTargetDateDesc(sessionId);

            if (documents.isEmpty() || documents.stream().anyMatch(this::hasInvalidWeatherAttributes)) {
                log.info(
                        "Daily weather data missing or invalid for session {}. Falling back to daily_zone aggregation.",
                        sessionId);
                ResultSession resultSession =
                        resultSessionRepository.findFirstByTypeOrderByCompletionDateDescOrThrow(DAILY_ZONE);
                documents = systemWeatherDao.aggregateDailyZonePredictions(resultSession.getId());
            }
        } else {
            documents =
                    systemWeatherDao.getPredictionsBySessionAndParentZoneOrderByTargetDateDesc(sessionId, parentZone);
        }

        return weatherAttributeResultDocumentMapper.mapSystemWeatherDocuments(limitDocuments(documents));
    }

    private boolean hasInvalidWeatherAttributes(SystemWeather systemWeather) {
        var weatherAgg = systemWeather.getResults().getWeatherAgg();
        return weatherAgg == null
                || isInvalidValue(weatherAgg.getPrecip())
                || isInvalidValue(weatherAgg.getWindGustMax())
                || isInvalidValue(weatherAgg.getWindSustained())
                || isInvalidValue(weatherAgg.getTemperatureMin())
                || isInvalidValue(weatherAgg.getTemperatureMean())
                || isInvalidValue(weatherAgg.getTemperatureMax());
    }

    private boolean isInvalidValue(Double value) {
        return value == null || Double.isNaN(value) || Double.isInfinite(value);
    }

    private List<SystemWeather> limitDocuments(List<SystemWeather> documents) {
        if (documents.size() > SYSTEM_DAILY_PREDICTIONS_LIMIT) {
            return documents.subList(documents.size() - SYSTEM_DAILY_PREDICTIONS_LIMIT, documents.size());
        }
        return documents;
    }
}

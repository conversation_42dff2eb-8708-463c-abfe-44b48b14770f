package com.esource.stormweatherservice.service;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import com.esource.stormweatherservice.config.properties.RetrospectiveProperties;
import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.RegionalWeather;
import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.repository.RegionalWeatherRepository;
import com.esource.stormweatherservice.repository.ResultSessionRepository;
import com.esource.stormweatherservice.service.mapper.WeatherAttributeResultDocumentMapper;

import lombok.RequiredArgsConstructor;

import static java.time.ZoneOffset.UTC;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Service
@RequiredArgsConstructor
public class RegionalWeatherService {

    private final RegionalWeatherRepository regionalWeatherRepository;
    private final ResultSessionRepository sessionRepository;
    private final RetrospectiveProperties retrospectiveProperties;
    private final WeatherAttributeResultDocumentMapper weatherAttributeResultDocumentMapper;

    public String getLatestSession(LatestSessionRequestDto requestDto) {
        ResultSession session;
        if (nonNull(requestDto.targetDate())) {
            ZonedDateTime adjustedTargetDate = requestDto
                    .targetDate()
                    .plusHours(retrospectiveProperties.getLookAheadHours())
                    .withZoneSameInstant(UTC)
                    .truncatedTo(ChronoUnit.HOURS);

            session = sessionRepository.findByTargetTypeAndTargetDateOrThrow(requestDto.type(), adjustedTargetDate);
        } else {
            session = sessionRepository.findFirstByTypeOrderByCompletionDateDescOrThrow(requestDto.type());
        }

        return session.getId().toHexString();
    }

    public List<WeatherAttributeResultDocument> getAllResultsBySession(
            String session, String parentZone, String targetType) {
        var sessionId = new ObjectId(session);

        // Retrieve the session to get the target date for proper date range calculation
        ResultSession resultSession = sessionRepository
                .findById(sessionId)
                .orElseThrow(() -> new IllegalArgumentException("Session not found: " + session));

        // Calculate end date based on the session's target date instead of current date
        // This ensures we get the correct 5-day range relative to the requested target date
        LocalDate sessionTargetDate = resultSession.getTargetDate().toLocalDate();
        String endDate = sessionTargetDate.plusDays(5).toString();

        List<RegionalWeather> documents;
        if (isEmpty(parentZone)) {
            documents =
                    regionalWeatherRepository.findBySessionAndTargetTypeAndDayBefore(sessionId, targetType, endDate);
        } else {
            documents = regionalWeatherRepository.findBySessionAndParentZoneAndTargetTypeAndDayBefore(
                    sessionId, parentZone, targetType, endDate);
        }

        return weatherAttributeResultDocumentMapper.mapRegionalWeatherDocuments(documents);
    }
}

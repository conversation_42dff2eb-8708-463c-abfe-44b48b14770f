package com.esource.stormweatherservice.service.mapper;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.esource.stormweatherservice.model.RegionalWeather;
import com.esource.stormweatherservice.model.Result;
import com.esource.stormweatherservice.model.SystemWeather;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;

@Mapper(componentModel = "spring")
public interface WeatherAttributeResultDocumentMapper {

    @Mapping(target = "name", expression = "java(mapNameToIdentifier(doc.getResults()))")
    @Mapping(target = "displayName", expression = "java(mapDisplayName(doc.getResults()))")
    @Mapping(source = "results.day", target = "day")
    @Mapping(source = "results.weatherAgg.precip", target = "weatherAttributes.precip")
    @Mapping(source = "results.weatherAgg.windGustMax", target = "weatherAttributes.windGustMax")
    @Mapping(source = "results.weatherAgg.windSustained", target = "weatherAttributes.windSustained")
    @Mapping(source = "results.weatherAgg.temperatureMinFahrenheit", target = "weatherAttributes.temperatureMin")
    @Mapping(source = "results.weatherAgg.temperatureMeanFahrenheit", target = "weatherAttributes.temperatureMean")
    @Mapping(source = "results.weatherAgg.temperatureMaxFahrenheit", target = "weatherAttributes.temperatureMax")
    @Mapping(source = "results.weatherAgg.threatLevel", target = "weatherAttributes.threatLevel")
    @Mapping(source = "results.weatherAgg.threatLevelCategory", target = "weatherAttributes.threatLevelCategory")
    @Mapping(source = "results.weatherAgg.cloudCoverPercentMean", target = "weatherAttributes.cloudCoverPercentMean")
    WeatherAttributeResultDocument mapRegionalWeather(RegionalWeather doc);

    @Mapping(source = "results.day", target = "day")
    @Mapping(target = "name", constant = "System")
    @Mapping(target = "displayName", constant = "System")
    @Mapping(source = "results.weatherAgg.precip", target = "weatherAttributes.precip")
    @Mapping(source = "results.weatherAgg.windGustMax", target = "weatherAttributes.windGustMax")
    @Mapping(source = "results.weatherAgg.windSustained", target = "weatherAttributes.windSustained")
    @Mapping(source = "results.weatherAgg.temperatureMinFahrenheit", target = "weatherAttributes.temperatureMin")
    @Mapping(source = "results.weatherAgg.temperatureMeanFahrenheit", target = "weatherAttributes.temperatureMean")
    @Mapping(source = "results.weatherAgg.temperatureMaxFahrenheit", target = "weatherAttributes.temperatureMax")
    @Mapping(source = "results.weatherAgg.threatLevel", target = "weatherAttributes.threatLevel")
    @Mapping(source = "results.weatherAgg.threatLevelCategory", target = "weatherAttributes.threatLevelCategory")
    @Mapping(source = "results.weatherAgg.cloudCoverPercentMean", target = "weatherAttributes.cloudCoverPercentMean")
    WeatherAttributeResultDocument mapSystemWeather(SystemWeather systemWeather);

    List<WeatherAttributeResultDocument> mapSystemWeatherDocuments(List<SystemWeather> systemWeatherDocuments);

    List<WeatherAttributeResultDocument> mapRegionalWeatherDocuments(List<RegionalWeather> regionalWeatherDocuments);

    default String mapNameToIdentifier(Result result) {
        return Stream.of(
                        Optional.ofNullable(result).map(Result::getRegion),
                        Optional.ofNullable(result).map(Result::getParentZone))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .map(name -> name.toLowerCase().replaceAll("\\s+", "-"))
                .orElse("unknown");
    }

    default String mapDisplayName(Result result) {
        return Stream.of(
                        Optional.ofNullable(result).map(Result::getDisplayZone),
                        Optional.ofNullable(result).map(Result::getParentZone),
                        Optional.ofNullable(result).map(Result::getRegion))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .findFirst()
                .orElse("Unknown");
    }
}

package com.esource.stormweatherservice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.rest.core.config.RepositoryRestConfiguration;
import org.springframework.data.rest.webmvc.config.RepositoryRestConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;

import com.esource.stormweatherservice.model.RegionalWeather;

/**
 * Adds certain REST configurations that we need in the application
 */
@Configuration
public class StormWeatherServiceRestConfig implements RepositoryRestConfigurer {
    @Override
    public void configureRepositoryRestConfiguration(RepositoryRestConfiguration config, CorsRegistry cors) {

        // by default Spring Boot does not expose @id annotation in the json response body
        config.exposeIdsFor(RegionalWeather.class);
    }
}

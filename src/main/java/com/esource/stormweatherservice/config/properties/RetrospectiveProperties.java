package com.esource.stormweatherservice.config.properties;

import java.util.Objects;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.ConstructorBinding;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@ConfigurationProperties(prefix = "retrospective")
@Slf4j
public class RetrospectiveProperties {

    private final Integer lookAheadHours;

    @ConstructorBinding
    public RetrospectiveProperties(Integer lookAheadHours) {
        this.lookAheadHours = Objects.requireNonNullElse(lookAheadHours, 0);
        log.info("Look Head Hours: {}", this.lookAheadHours);
    }
}

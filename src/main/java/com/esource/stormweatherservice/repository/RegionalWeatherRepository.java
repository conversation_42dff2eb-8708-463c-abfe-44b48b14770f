package com.esource.stormweatherservice.repository;

import java.util.List;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.esource.stormweatherservice.model.RegionalWeather;

/**
 * Abstraction layer between {@link RegionalWeather} and the underlying storage
 * mechanism
 */
public interface RegionalWeatherRepository extends MongoRepository<RegionalWeather, String> {
    /**
     * Find the latest document by type
     *
     * @param type the type of document to find
     * @return the latest document of the given type
     *
     *         Example: find the latest document of type "forecast"
     *
     *         <pre>
     * {@code
     * Optional<RegionalWeather> latestForecast = regionalWeatherRepository.findFirstByTypeOrderByTargetDateDesc("daily_zone");
     * }
     * </pre>
     *
     *         Returns the latest document of type "daily_zone"
     *
     *         <pre>
     * {
     *  "_id": "60b9b0c9e4b0c9a7c8f0b0a0",
     *  "targetType": "daily_zone",
     *  "targetDate": "2021-06-04T00:00:00.000+00:00",
     *  "session": "60b9b0c9e4b0c9a7c8f0b0a0",
     *  "results": {
     *    "day": "2021-06-04",
     *    "zone": "zone1",
     *    "weatherAgg": {
     *         "precip": 0.0,
     *         "windGustMax": 0.0,
     *         "windGustSustained": 0.0
     *     }
     *   }
     * }
     *         </pre>
     *
     *         "
     */
    @Deprecated
    @Query(sort = "{'results.generationTime': -1}")
    Optional<RegionalWeather> findFirstByType(String type);

    /**
     * Find all documents by session
     *
     * @param session the session id to find
     * @param endDate the end date to find
     * @return all documents with the given session id
     *
     *         Example: find all documents with session id
     *         "60b9b0c9e4b0c9a7c8f0b0a0" and end date "2021-06-04"
     *
     *         <pre>
     * {@code
     * List<RegionalWeather> dailyZone = regionalWeatherRepository.findBySessionAndEndDate("60b9b0c9e4b0c9a7c8f0b0a0", "2021-06-04");
     * }
     * </pre>
     *
     *         Returns all documents with session id "60b9b0c9e4b0c9a7c8f0b0a0" and
     *         end date "2021-06-04"
     *
     *         <pre>
     * [
     * {
     * "_id": "60b9b0c9e4b0c9a7c8f0b0a0",
     * "targetType": "daily_zone",
     * "targetDate": "2021-06-04T00:00:00.000+00:00",
     * "session": "60b9b0c9e4b0c9a7c8f0b0a0",
     * "results": {
     *     "day": "2021-06-04",
     *     "zone": "zone1",
     *     "weatherAgg": {
     *         "precip": 0.0,
     *         "windGustMax": 0.0,
     *         "windGustSustained": 0.0
     *    }
     * }
     * },
     * {
     * "_id": "60b9b0c9e4b0c9a7c8f0b0a1",
     * "targetType": "daily_zone",
     * "targetDate": "2021-06-03T00:00:00.000+00:00",
     * "session": "60b9b0c9e4b0c9a7c8f0b0a0",
     * "results": {
     *     "day": "2021-06-03",
     *     "zone": "zone1",
     *     "weatherAgg": {
     *        "precip": 0.0,
     *        "windGustMax": 0.0,
     *        "windGustSustained": 0.0
     *     }
     *   }
     * }
     * ]
     *         </pre>
     */
    @Query("{'session' : ?0, 'results.day' : { $lt : ?1}}")
    List<RegionalWeather> findBySessionAndDayBefore(ObjectId session, String dayBefore);

    @Query(
            value =
                    """
                    {
                        'session': ?0,
                        'results.parentZone': ?1,
                        'results.day' : { $lt : ?2 },
                        'targetType': 'daily_zone'
                    }
                    """)
    List<RegionalWeather> findBySessionAndParentZoneAndDayBefore(
            ObjectId objectId, String parentZone, String dayBefore);

    @Query(
            value =
                    """
                    {
                        'session': ?0,
                        'results.parentZone': ?1,
                        'targetType': ?2,
                        'results.day' : { $lt : ?3 }
                    }
                    """)
    List<RegionalWeather> findBySessionAndParentZoneAndTargetTypeAndDayBefore(
            ObjectId objectId, String parentZone, String targetType, String dayBefore);

    @Query("{'session' : ?0, 'targetType': ?1, 'results.day' : { $lt : ?2}}")
    List<RegionalWeather> findBySessionAndTargetTypeAndDayBefore(ObjectId session, String targetType, String dayBefore);
}

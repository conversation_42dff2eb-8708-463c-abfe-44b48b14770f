package com.esource.stormweatherservice.repository;

import java.util.ArrayList;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

import com.esource.stormweatherservice.model.SystemWeather;

import lombok.RequiredArgsConstructor;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;

@Repository
@RequiredArgsConstructor
public class SystemWeatherDao {

    private final MongoTemplate mongoTemplate;

    public List<SystemWeather> getPredictionsBySessionAndParentZoneOrderByTargetDateDesc(
            ObjectId session, String parentZone) {
        List<AggregationOperation> aggregationOperations =
                buildAggregationPipelineForSessionAndParentZone(session, parentZone);
        return executePredictionAggregation(aggregationOperations);
    }

    public List<SystemWeather> aggregateDailyZonePredictions(ObjectId session) {
        List<AggregationOperation> aggregationOperations = buildAggregationPipeline(session);
        return executePredictionAggregation(aggregationOperations);
    }

    private static List<AggregationOperation> buildAggregationPipelineForSessionAndParentZone(
            ObjectId session, String parentZone) {
        List<AggregationOperation> aggregationOperations = new ArrayList<>();

        aggregationOperations.add(match(Criteria.where("targetType")
                .is("daily_zone")
                .and("session")
                .is(session)
                .and("results.parentZone")
                .is(parentZone)));
        aggregationOperations.add(groupBySessionAndDay());
        aggregationOperations.add(projectAggregatedResults());
        aggregationOperations.add(sortAggregationResults("targetDate"));

        return aggregationOperations;
    }

    private List<AggregationOperation> buildAggregationPipeline(ObjectId session) {
        List<AggregationOperation> aggregationOperations = new ArrayList<>();

        aggregationOperations.add(match(
                Criteria.where("targetType").is("daily_zone").and("session").is(session)));
        aggregationOperations.add(groupBySessionAndDay());
        aggregationOperations.add(projectAggregatedResults());
        aggregationOperations.add(sortAggregationResults("targetDate"));

        return aggregationOperations;
    }

    private List<SystemWeather> executePredictionAggregation(List<AggregationOperation> aggregationOperations) {
        List<SystemWeather> predictions = mongoTemplate
                .aggregate(Aggregation.newAggregation(aggregationOperations), "result_document", SystemWeather.class)
                .getMappedResults();

        predictions.forEach(prediction -> prediction.setType("daily"));

        return predictions;
    }

    private static GroupOperation groupBySessionAndDay() {
        Fields idFields = Fields.from(Fields.field("session", "$session"), Fields.field("day", "$results.day"));
        return Aggregation.group(idFields)
                .first("$targetDate")
                .as("targetDate")
                .first("$results.generationTime")
                .as("generationTime")
                .max("$results.weatherAgg.precip_inch_sum")
                .as("precip_inch_sum")
                .max("$results.weatherAgg.gust_mph_amax")
                .as("gust_mph_amax")
                .avg("$results.weatherAgg.wind_mph_mean")
                .as("wind_mph_mean")
                .min("$results.weatherAgg.temp_c_amin")
                .as("temp_c_amin")
                .avg("$results.weatherAgg.temp_c_mean")
                .as("temp_c_mean")
                .max("$results.weatherAgg.temp_c_amax")
                .as("temp_c_amax")
                .min("$results.weatherAgg.temp_f_amin")
                .as("temp_f_amin")
                .avg("$results.weatherAgg.temp_f_mean")
                .as("temp_f_mean")
                .max("$results.weatherAgg.temp_f_amax")
                .as("temp_f_amax")
                .avg("$results.weatherAgg.cloud_cover_percent_mean")
                .as("cloud_cover_percent_mean")
                .max("$results.weatherAgg.spc_sow_amax")
                .as("spc_sow_amax");
    }

    private static ProjectionOperation projectAggregatedResults() {
        return Aggregation.project()
                .andExclude("_id")
                .and("$_id.session")
                .as("session")
                .and("targetDate")
                .as("targetDate")
                .and("$_id.day")
                .as("results.day")
                .and("generationTime")
                .as("results.generationTime")
                .and("precip_inch_sum")
                .as("results.weatherAgg.precip_inch_sum")
                .and("gust_mph_amax")
                .as("results.weatherAgg.gust_mph_amax")
                .and("wind_mph_mean")
                .as("results.weatherAgg.wind_mph_mean")
                .and("temp_c_amin")
                .as("results.weatherAgg.temp_c_amin")
                .and("temp_c_mean")
                .as("results.weatherAgg.temp_c_mean")
                .and("temp_c_amax")
                .as("results.weatherAgg.temp_c_amax")
                .and("temp_f_amin")
                .as("results.weatherAgg.temp_f_amin")
                .and("temp_f_mean")
                .as("results.weatherAgg.temp_f_mean")
                .and("temp_f_amax")
                .as("results.weatherAgg.temp_f_amax")
                .and("cloud_cover_percent_mean")
                .as("results.weatherAgg.cloud_cover_percent_mean")
                .and("spc_sow_amax")
                .as("results.weatherAgg.spc_sow_amax");
    }

    private static SortOperation sortAggregationResults(String... fields) {
        return sort(Sort.by(Sort.Direction.DESC, fields));
    }
}

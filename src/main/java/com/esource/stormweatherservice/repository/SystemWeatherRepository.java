package com.esource.stormweatherservice.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.esource.stormweatherservice.model.SystemWeather;

/**
 * Abstraction layer between {@link SystemWeather} and the underlying storage
 * mechanism
 */
public interface SystemWeatherRepository extends MongoRepository<SystemWeather, String> {

    @Query("{'session' : ?0, 'results.day' : { $lt : ?1}}")
    List<SystemWeather> findBySession(ObjectId session, String endDate);

    // A session can contain a maximum of 6 predictions
    List<SystemWeather> findTop6BySessionOrderByTargetDateDesc(ObjectId objectId);
}

package com.esource.stormweatherservice.repository;

import java.time.ZonedDateTime;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.service.exception.DomainObjectNotFoundException;

import lombok.NonNull;

@Repository
public interface ResultSessionRepository extends MongoRepository<ResultSession, ObjectId> {

    Optional<ResultSession> findFirstByTargetTypeOrderByCompletionDateDesc(String type);

    Optional<ResultSession> findByTargetTypeAndTargetDate(String type, ZonedDateTime date);

    default ResultSession findFirstByTypeOrderByCompletionDateDescOrThrow(@NonNull String type) {
        return findFirstByTargetTypeOrderByCompletionDateDesc(type)
                .orElseThrow(
                        () -> new DomainObjectNotFoundException("'ResultSession' not found, type=%s".formatted(type)));
    }

    default ResultSession findByTargetTypeAndTargetDateOrThrow(
            @NonNull String type, @NonNull ZonedDateTime targetDate) {
        return findByTargetTypeAndTargetDate(type, targetDate)
                .orElseThrow(() -> new DomainObjectNotFoundException(
                        "'ResultSession' not found: type=%s, targetDate=%s".formatted(type, targetDate)));
    }
}

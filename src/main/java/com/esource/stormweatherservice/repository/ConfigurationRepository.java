package com.esource.stormweatherservice.repository;

import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.esource.stormweatherservice.model.Configurations;

@Repository
public interface ConfigurationRepository extends MongoRepository<Configurations, ObjectId> {

    @Query("{'type': ?0, 'configs': {$elemMatch: {'type': ?1}}}")
    List<Configurations> findByTypeAndConfigType(String type, String configType);

    List<Configurations> findByType(String type);
}

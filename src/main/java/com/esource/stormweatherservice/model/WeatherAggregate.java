package com.esource.stormweatherservice.model;

import org.springframework.data.mongodb.core.mapping.Field;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WeatherAggregate represents the weather attributes for a given day and
 * region.
 * precip - the total precipitation for the day in inches
 * windGustMax - the maximum wind gust for the day in mph
 * windSustained - the average wind speed for the day in mph
 * <p>
 * Example WeatherAggregate JSON representation:
 *
 * <pre>
 * {
 *  "precip": 1.0,
 *  "windGustMax": 2.0,
 *  "windGustSustained": 3.0
 * }
 * </pre>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeatherAggregate {
    @Field("precip_inch_sum")
    private Double precip;

    @Field("gust_mph_amax")
    private Double windGustMax;

    @Field("wind_mph_mean")
    private Double windSustained;

    @Field("temp_c_amin")
    private Double temperatureMin;

    @Field("temp_c_mean")
    private Double temperatureMean;

    @Field("temp_c_amax")
    private Double temperatureMax;

    @Field("temp_f_amin")
    private Double temperatureMinFahrenheit;

    @Field("temp_f_mean")
    private Double temperatureMeanFahrenheit;

    @Field("temp_f_amax")
    private Double temperatureMaxFahrenheit;

    @Field("spc_sow_amax")
    private Double threatLevel;

    @Field("spc_sow_amax_cat")
    private String threatLevelCategory;

    @Field("cloud_cover_percent_mean")
    private Double cloudCoverPercentMean;
}

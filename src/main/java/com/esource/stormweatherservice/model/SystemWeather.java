package com.esource.stormweatherservice.model;

import java.util.Date;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a Regional Weather entity in the domain model.
 * <p>
 * A Regional Weather can be uniquely identified by its {@code _id}.
 * </p>
 * <p>
 * The {@code id} maps to a primary key in the MongoDB collection.
 * </p>
 * <p>
 * Example Regional Weather JSON representation:
 *
 * <pre>
 * {
 *      "_id": "5f9b2b9b9d6b1d1b4c9b4b4b",
 *      "type": "zone",
 *      "targetDate": "2023-04-26T00:00:00.000+00:00",
 *      "session": "5f9b2b9b9d6b1d1b4c9b4b4b",
 *      "results": {
 *         "day": "2023-04-26",
 *         "zone": "zone1",
 *         "weatherAgg": {
 *            "precip": 1.0,
 *            "windGustMax": 2.0,
 *            "windGustSustained": 3.0
 *        }
 * }
 * </pre>
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "result_document")
public class SystemWeather {

    @Id
    private String id;

    @Field("targetType")
    private String type;

    private Date targetDate;
    private ObjectId session;
    private SystemResult results;
}

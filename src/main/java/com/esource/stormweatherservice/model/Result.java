package com.esource.stormweatherservice.model;

import java.time.Instant;

import org.springframework.data.mongodb.core.mapping.Field;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Result {
    private String day;

    private String parentZone;

    @Field("zone")
    private String region;

    private String displayZone;

    @Field("generationTimeExact")
    private Instant generationTime;

    private WeatherAggregate weatherAgg;
}

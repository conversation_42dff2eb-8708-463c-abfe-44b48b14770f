package com.esource.stormweatherservice.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WeatherAttributeResultDocument {
    private String name;
    private String day;
    private String displayName;
    private WeatherAttributes weatherAttributes;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WeatherAttributes {
        private Double precip;
        private Double windGustMax;
        private Double windSustained;
        private Double temperatureMin;
        private Double temperatureMean;
        private Double temperatureMax;
        private Double threatLevel;
        private String threatLevelCategory;
        private Double cloudCoverPercentMean;
    }
}

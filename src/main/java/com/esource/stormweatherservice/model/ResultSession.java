package com.esource.stormweatherservice.model;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "result_session")
public class ResultSession {

    private ObjectId id;
    private String targetType;
    private ZonedDateTime targetDate;
    private LocalDateTime completionDate;
}

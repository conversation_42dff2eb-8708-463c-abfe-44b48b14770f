package com.esource.stormweatherservice.http.dto;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorDto {

    public ErrorDto(String message, LocalDateTime timestamp) {
        this.message = message;
        this.timestamp = timestamp;
    }

    private String message;
    private LocalDateTime timestamp;
}

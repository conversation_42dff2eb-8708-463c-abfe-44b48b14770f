package com.esource.stormweatherservice.http.exception;

import java.time.LocalDateTime;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import com.esource.stormweatherservice.http.dto.ErrorDto;
import com.esource.stormweatherservice.service.exception.DomainObjectNotFoundException;

import lombok.extern.slf4j.Slf4j;

import static java.time.ZoneOffset.UTC;

@ControllerAdvice
@Slf4j
public class WebExceptionHandler {

    @ExceptionHandler(DomainObjectNotFoundException.class)
    public ResponseEntity<ErrorDto> handleDomainObjectNotFound(DomainObjectNotFoundException ex) {
        return new ResponseEntity<>(new ErrorDto(ex.getMessage(), LocalDateTime.now(UTC)), HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorDto> globalExceptionHandling(Exception exception) {
        log.error(exception.getMessage(), exception);
        return new ResponseEntity<>(
                new ErrorDto("Internal Server Error.", LocalDateTime.now()), HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

package com.esource.stormweatherservice.http.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.service.RegionalWeatherService;

import lombok.RequiredArgsConstructor;

/**
 * @deprecated use {@link BaseController} instead
 * pending removal in the next release
 */
@Deprecated(forRemoval = true)
@RestController
@RequiredArgsConstructor
@RequestMapping("/weather/search")
public class RegionalWeatherController {

    @Autowired
    private RegionalWeatherService regionalWeatherService;

    public RegionalWeatherController(RegionalWeatherService regionalWeatherService) {
        this.regionalWeatherService = regionalWeatherService;
    }

    @GetMapping("/latest-session/{type}")
    public String getLatestSessionByType(@PathVariable String type) {
        return regionalWeatherService.getLatestSession(
                LatestSessionRequestDto.builder().type(type).build());
    }

    @GetMapping("/session/{session}")
    public ResponseEntity<List<WeatherAttributeResultDocument>> getDocumentsBySession(@PathVariable String session) {
        return ResponseEntity.ok(regionalWeatherService.getAllResultsBySession(session, null, "daily_zone"));
    }
}

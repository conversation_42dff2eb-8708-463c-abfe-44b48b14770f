package com.esource.stormweatherservice.http.controller;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormweatherservice.model.Configurations;
import com.esource.stormweatherservice.service.ConfigurationService;

@RestController
@RequestMapping("/chartConfigs")
public class ChartConfigController {

    public static String CHART_TYPE = "chart";

    @Autowired
    private ConfigurationService configurationService;

    @Deprecated
    @GetMapping("/{configType}")
    public ResponseEntity<Configurations> getConfigs(@PathVariable String configType) {
        if (configType == null || configType.equals("")) {
            return ResponseEntity.badRequest().build();
        }
        Optional<Configurations> configurations =
                configurationService.getConfigurationsByTypeAndConfigType(CHART_TYPE, configType).stream()
                        .findFirst();
        return configurations.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound()
                .build());
    }

    @GetMapping
    public ResponseEntity<List<Configurations>> getAllChartConfigs() {
        return ResponseEntity.ok(configurationService.getAllConfigurationsByType(CHART_TYPE));
    }
}

package com.esource.stormweatherservice.http.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.service.RegionalWeatherService;
import com.esource.stormweatherservice.service.SystemWeatherService;

import lombok.extern.slf4j.Slf4j;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;

@Slf4j
@RestController
public class BaseController {

    private static final String DAILY_ZONE = "daily_zone";
    private static final String DAILY_PARENT_ZONE = "daily_parent_zone";
    private static final String DAILY = "daily";

    @Autowired
    private RegionalWeatherService regionalWeatherService;

    @Autowired
    private SystemWeatherService systemWeatherService;

    public BaseController(RegionalWeatherService regionalWeatherService, SystemWeatherService systemWeatherService) {
        this.regionalWeatherService = regionalWeatherService;
        this.systemWeatherService = systemWeatherService;
    }

    @Deprecated(forRemoval = true)
    @GetMapping("/weatherAttributes/{type}/latest")
    public String getLatestSessionByType(@PathVariable String type) {
        return regionalWeatherService.getLatestSession(
                LatestSessionRequestDto.builder().type(type).build());
    }

    @GetMapping("/weatherAttributes/sessions/latest")
    public String getLatestSession(@Validated LatestSessionRequestDto requestDto) {
        return regionalWeatherService.getLatestSession(requestDto);
    }

    @GetMapping("/weatherAttributes/sessions/{session}")
    public ResponseEntity<List<WeatherAttributeResultDocument>> getDocumentsBySession(
            @PathVariable String session,
            @RequestParam(value = "targetType", defaultValue = "daily_zone") String targetType,
            @RequestParam(required = false) String parentZone) {
        String trimmedParentZone = null;
        if (!isEmpty(parentZone)) {
            trimmedParentZone = parentZone.replace("-", " ");
        }
        if (targetType.equals(DAILY_ZONE) || targetType.equals(DAILY_PARENT_ZONE)) {
            return ResponseEntity.ok(
                    regionalWeatherService.getAllResultsBySession(session, trimmedParentZone, targetType));
        } else if (targetType.equals(DAILY)) {
            return ResponseEntity.ok(systemWeatherService.getAllResultsBySession(session, trimmedParentZone));
        }

        log.warn("Unsupported targetType requested: {}", targetType);
        return ResponseEntity.badRequest().build();
    }
}

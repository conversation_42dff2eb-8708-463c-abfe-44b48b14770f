package com.esource.stormweatherservice.repository;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.esource.stormweatherservice.helpers.AbstractTestContainer;
import com.esource.stormweatherservice.model.*;

import static org.junit.Assert.assertEquals;

class SystemWeatherRepositoryTest extends AbstractTestContainer {
    @Autowired
    private WebApplicationContext context;

    private MockMvc mvc;

    @Autowired
    private SystemWeatherRepository repository;

    @BeforeEach
    void initialize() {
        mvc = MockMvcBuilders.webAppContextSetup(context)
                .defaultRequest(MockMvcRequestBuilders.get("/").locale(Locale.US))
                .build();

        repository.saveAll(createSystemWeather());
    }

    @AfterEach
    void tearDown() {
        repository.deleteAll();
        repository = null;
    }

    static List<SystemWeather> createSystemWeather() {
        return List.of(
                SystemWeather.builder()
                        .session(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8c"))
                        .type("daily")
                        .targetDate(new Date())
                        .results(SystemResult.builder()
                                .day("2020-10-29")
                                .generationTime(Instant.parse("2020-10-29T00:00:00.000Z"))
                                .weatherAgg(WeatherAggregate.builder()
                                        .precip(0.0)
                                        .windGustMax(3.24)
                                        .windSustained(1.62)
                                        .build())
                                .build())
                        .build(),
                SystemWeather.builder()
                        .session(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8c"))
                        .type("daily")
                        .targetDate(new Date())
                        .results(SystemResult.builder()
                                .day("2020-10-30")
                                .generationTime(Instant.parse("2020-10-30T00:00:00.000Z"))
                                .weatherAgg(WeatherAggregate.builder()
                                        .precip(0.0)
                                        .windGustMax(12.0)
                                        .windSustained(6.0)
                                        .build())
                                .build())
                        .build(),
                SystemWeather.builder()
                        .session(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8c"))
                        .type("daily")
                        .targetDate(new Date())
                        .results(SystemResult.builder()
                                .day("2020-10-31")
                                .generationTime(Instant.parse("2020-10-31T00:00:00.000Z"))
                                .weatherAgg(WeatherAggregate.builder()
                                        .precip(0.0)
                                        .windGustMax(0.0)
                                        .windSustained(4.0)
                                        .build())
                                .build())
                        .build(),
                SystemWeather.builder()
                        .session(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8d"))
                        .type("daily")
                        .targetDate(new Date())
                        .results(SystemResult.builder()
                                .day("2020-11-01")
                                .generationTime(Instant.parse("2020-11-01T00:00:00.000Z"))
                                .weatherAgg(WeatherAggregate.builder()
                                        .precip(0.5)
                                        .windGustMax(6.6)
                                        .windSustained(5.0)
                                        .build())
                                .build())
                        .build());
    }

    @Test
    void testFindBySessionCutoffDate() {
        var result = repository.findBySession(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8c"), "2020-10-31");

        assertEquals(2, result.size());
        assertEquals("5f9f1b9b4b6b8b1b8c8c8c8c", result.get(0).getSession().toString());
    }

    @Test
    void testFindBySession() {
        var result = repository.findBySession(new ObjectId("5f9f1b9b4b6b8b1b8c8c8c8d"), "2020-11-02");

        assertEquals(1, result.size());
        assertEquals("5f9f1b9b4b6b8b1b8c8c8c8d", result.get(0).getSession().toString());
        assertEquals("2020-11-01", result.get(0).getResults().getDay());
    }

    @Test
    void testEmptyFindBySession() {
        var result = repository.findBySession(new ObjectId("5f9f1b9b4b6a8b1b8c8c8c8c"), "2020-11-03");

        assertEquals(0, result.size());
    }
}

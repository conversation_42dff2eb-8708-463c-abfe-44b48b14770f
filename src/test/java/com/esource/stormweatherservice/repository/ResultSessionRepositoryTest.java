package com.esource.stormweatherservice.repository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.stormweatherservice.helpers.AbstractTestContainer;
import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.service.exception.DomainObjectNotFoundException;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class ResultSessionRepositoryTest extends AbstractTestContainer {

    @Autowired
    private ResultSessionRepository testable;

    @BeforeEach
    void initialize() {
        testable.saveAll(createResultSessions());
    }

    @AfterEach
    void tearDown() {
        testable.deleteAll();
    }

    static LocalDateTime makeDate() {
        return LocalDateTime.of(2023, 11, 11, 11, 11, 11, 0);
    }

    static List<ResultSession> createResultSessions() {

        var oldestDate = LocalDateTime.of(1970, 1, 1, 0, 0, 0, 0);

        return Arrays.asList(
                ResultSession.builder()
                        .id(new ObjectId("60c72b2f5f1b2c6d88f5c4a8"))
                        .completionDate(makeDate())
                        .targetDate(makeDate().atZone(UTC))
                        .targetType("daily")
                        .build(),
                ResultSession.builder()
                        .id(new ObjectId("60c72b2f5f1b2c6d88f5c4a9"))
                        .completionDate(makeDate().plusDays(1))
                        .targetDate(makeDate().plusDays(1).atZone(UTC))
                        .targetType("daily")
                        .build(),
                ResultSession.builder()
                        .id(new ObjectId("60c72b2f5f1b2c6d88f5c4b9"))
                        .completionDate(oldestDate.plusDays(3))
                        .targetDate(oldestDate.plusDays(3).atZone(UTC))
                        .targetType("type-b")
                        .build(),
                ResultSession.builder()
                        .id(new ObjectId("60c72b2f5f1b2c6d88f5c4c8"))
                        .completionDate(makeDate().plusDays(4))
                        .targetDate(makeDate().plusDays(4).atZone(UTC))
                        .targetType("daily")
                        .build(),
                ResultSession.builder()
                        .id(new ObjectId("60c72b2f5f1b2c6d88f5c4b8"))
                        .completionDate(makeDate().plusDays(2))
                        .targetDate(makeDate().plusDays(2).atZone(UTC))
                        .targetType("type-b")
                        .build());
    }

    @Test
    void testFindByTargetTypeAndTargetDateOrderByCompletionDateDesc() {
        String type = "type-b";

        ResultSession actualResultSession = testable.findFirstByTypeOrderByCompletionDateDescOrThrow(type);

        assertThat(actualResultSession.getId()).isEqualTo(new ObjectId("60c72b2f5f1b2c6d88f5c4b8"));
    }

    @Test
    void testFindByTypeAndTargetDateOrderByCompletionDateDescWhenResultSessionDoesNotExist() {
        String type = "daily";
        LocalDateTime targetDate = makeDate().plusDays(2);

        assertThatThrownBy(() -> testable.findByTargetTypeAndTargetDateOrThrow(type, targetDate.atZone(UTC)))
                .isInstanceOf(DomainObjectNotFoundException.class)
                .hasMessageStartingWith("'ResultSession' not found");
    }

    @Test
    void testFindByTargetTypeAndTargetDate() {
        String type = "daily";
        LocalDateTime targetDate = makeDate().plusDays(1);

        ResultSession actualResultSession = testable.findByTargetTypeAndTargetDateOrThrow(type, targetDate.atZone(UTC));

        assertThat(actualResultSession.getId()).isEqualTo(new ObjectId("60c72b2f5f1b2c6d88f5c4a9"));
    }

    @Test
    void testFindByTargetTypeAndTargetDateWhenResultSessionDoesNotExist() {
        String type = "test-b";
        LocalDateTime targetDate = makeDate().plusDays(1);

        assertThatThrownBy(() -> testable.findByTargetTypeAndTargetDateOrThrow(type, targetDate.atZone(UTC)))
                .isInstanceOf(DomainObjectNotFoundException.class)
                .hasMessageStartingWith("'ResultSession' not found");
    }
}

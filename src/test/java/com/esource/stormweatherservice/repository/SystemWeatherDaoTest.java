package com.esource.stormweatherservice.repository;

import java.time.Instant;
import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.stormweatherservice.helpers.AbstractTestContainer;
import com.esource.stormweatherservice.model.RegionalWeather;
import com.esource.stormweatherservice.model.SystemResult;
import com.esource.stormweatherservice.model.SystemWeather;
import com.esource.stormweatherservice.model.WeatherAggregate;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.SneakyThrows;

import static com.github.dockerjava.zerodep.shaded.org.apache.commons.codec.Resources.getInputStream;
import static org.assertj.core.api.Assertions.assertThat;

class SystemWeatherDaoTest extends AbstractTestContainer {

    @Autowired
    private SystemWeatherDao testable;

    @Autowired
    private RegionalWeatherRepository regionalWeatherRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @SneakyThrows
    void testGetPredictionsBySessionAndParentZone() {

        List<RegionalWeather> regionalDailyPredictions = objectMapper.readValue(
                getInputStream("json/testGetPredictionsBySessionAndParentZone_input.json"), new TypeReference<>() {});
        regionalWeatherRepository.saveAll(regionalDailyPredictions);

        List<SystemWeather> actual = testable.getPredictionsBySessionAndParentZoneOrderByTargetDateDesc(
                new ObjectId("66994b4a25abdbf5e98e8fe0"), "BIRMINGHAM");

        SystemWeather expectedResult = SystemWeather.builder()
                .targetDate(Date.from(Instant.parse("2024-07-15T15:00:00.000Z")))
                .session(new ObjectId("66994b4a25abdbf5e98e8fe0"))
                .type("daily")
                .results(SystemResult.builder()
                        .day("2024-07-15")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.00788)
                                .windSustained(5.118687500000001)
                                .windGustMax(14.7926)
                                .temperatureMin(11.6)
                                .temperatureMean(36.37083333333334)
                                .temperatureMax(30.2)
                                .temperatureMinFahrenheit(60.88)
                                .temperatureMeanFahrenheit(86.2675)
                                .temperatureMaxFahrenheit(84.56)
                                .threatLevel(0.0)
                                .cloudCoverPercentMean(44.541666666666664)
                                .build())
                        .build())
                .build();

        assertThat(actual).first().isEqualTo(expectedResult);
    }
}

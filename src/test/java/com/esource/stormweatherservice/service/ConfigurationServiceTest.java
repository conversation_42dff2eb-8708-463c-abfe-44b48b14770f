package com.esource.stormweatherservice.service;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.esource.stormweatherservice.model.Config;
import com.esource.stormweatherservice.model.Configurations;
import com.esource.stormweatherservice.repository.ConfigurationRepository;

import static com.esource.stormweatherservice.http.controller.ChartConfigController.CHART_TYPE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ConfigurationServiceTest {

    @Mock
    private ConfigurationRepository configurationRepository;

    @InjectMocks
    private ConfigurationService configurationService;

    @BeforeEach
    void setUp() {
        configurationRepository = mock(ConfigurationRepository.class);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetConfigsByTypeAndConfigType() {
        // Given
        String targetType = "weather";
        Config weatherConfig =
                Config.builder().type(targetType).displayName("weather1").build();
        Config weatherConfig2 =
                Config.builder().type(targetType).displayName("weather2").build();
        Config notWeatherConfig3 =
                Config.builder().type("notWeather").displayName("toBeExcluded").build();
        Configurations configs = Configurations.builder()
                .type(CHART_TYPE)
                .configs(List.of(weatherConfig, weatherConfig2, notWeatherConfig3))
                .build();
        when(configurationRepository.findByTypeAndConfigType(CHART_TYPE, targetType))
                .thenReturn(List.of(configs));

        // When
        List<Configurations> configurations =
                configurationService.getConfigurationsByTypeAndConfigType(CHART_TYPE, targetType);

        // Then
        Assertions.assertEquals(1, configurations.size());
        Assertions.assertEquals(2, configurations.get(0).getConfigs().size());
        Assertions.assertEquals(
                weatherConfig, configurations.get(0).getConfigs().get(0));
        Assertions.assertEquals(
                weatherConfig2, configurations.get(0).getConfigs().get(1));
    }

    @Test
    void testGetConfigsByTypeAndConfigTypeWhenErrorIsThrown() {
        // Given
        String targetType = "weather";

        Configurations configs = Configurations.builder().type(CHART_TYPE).build();
        when(configurationRepository.findByTypeAndConfigType(CHART_TYPE, targetType))
                .thenThrow(new RuntimeException("Error"));

        // When
        List<Configurations> configurations =
                configurationService.getConfigurationsByTypeAndConfigType(CHART_TYPE, targetType);

        // Then
        Assertions.assertEquals(0, configurations.size());
    }

    @Test
    void testGetAllConfigurationsByType() {
        Configurations configs = Configurations.builder().type(CHART_TYPE).build();
        when(configurationRepository.findByType(CHART_TYPE)).thenReturn(List.of(configs));

        // When
        List<Configurations> configurations = configurationService.getAllConfigurationsByType(CHART_TYPE);

        // Then
        Assertions.assertEquals(1, configurations.size());
        Assertions.assertEquals(configs, configurations.get(0));
    }

    @Test
    void testGetConfigsWhenErrorIsThrown() {
        // Given
        when(configurationRepository.findByType(CHART_TYPE)).thenThrow(new RuntimeException("Error"));

        // When
        List<Configurations> configurations = configurationService.getAllConfigurationsByType(CHART_TYPE);

        // Then
        Assertions.assertEquals(0, configurations.size());
    }
}

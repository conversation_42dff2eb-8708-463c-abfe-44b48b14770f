package com.esource.stormweatherservice.service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.esource.stormweatherservice.config.properties.RetrospectiveProperties;
import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.RegionalWeather;
import com.esource.stormweatherservice.model.Result;
import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.model.WeatherAggregate;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.repository.RegionalWeatherRepository;
import com.esource.stormweatherservice.repository.ResultSessionRepository;
import com.esource.stormweatherservice.service.exception.DomainObjectNotFoundException;
import com.esource.stormweatherservice.service.mapper.WeatherAttributeResultDocumentMapper;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = RegionalWeatherService.class)
class RegionalWeatherServiceTest {

    @MockBean
    private RegionalWeatherRepository regionalWeatherRepository;

    @MockBean
    private ResultSessionRepository sessionRepository;

    @MockBean
    private RetrospectiveProperties retrospectiveProperties;

    @MockBean
    private WeatherAttributeResultDocumentMapper weatherAttributeResultDocumentMapper;

    @Autowired
    private RegionalWeatherService testable;

    @Test
    void testGetLatestSessionByType() {
        String targetType = "daily_zone";
        ObjectId sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");

        ResultSession latestSession =
                ResultSession.builder().id(sessionID).targetType(targetType).build();

        when(sessionRepository.findFirstByTypeOrderByCompletionDateDescOrThrow(targetType))
                .thenReturn(latestSession);

        assertThat(latestSession.getId().toString())
                .isEqualTo(testable.getLatestSession(
                        LatestSessionRequestDto.builder().type(targetType).build()));
    }

    @Test
    void testGetAllResultsBySession_ReturnsMappedResults_WhenParentZoneIsNull() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");
        ZonedDateTime sessionTargetDate = ZonedDateTime.parse("2020-10-28T12:00:00Z");

        // Mock the session
        ResultSession resultSession = ResultSession.builder()
                .id(sessionID)
                .targetType("daily_zone")
                .targetDate(sessionTargetDate)
                .build();

        RegionalWeather regionalWeatherLatest = RegionalWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("North America")
                        .displayZone("United States")
                        .day("2020-10-28")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(90.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        RegionalWeather regionalWeatherLatest2 = RegionalWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("North America")
                        .displayZone("United States")
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(40.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        WeatherAttributeResultDocument expectedFirstDocument = WeatherAttributeResultDocument.builder()
                .displayName("United States")
                .name("north-america")
                .day("2020-10-28")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(90.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        WeatherAttributeResultDocument expectedSecondDocument = WeatherAttributeResultDocument.builder()
                .displayName("United States")
                .name("north-america")
                .day("2020-10-29")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(40.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        List<RegionalWeather> regionalWeatherDocuments = List.of(regionalWeatherLatest, regionalWeatherLatest2);
        List<WeatherAttributeResultDocument> expectedResult = List.of(expectedFirstDocument, expectedSecondDocument);
        String endDate = sessionTargetDate.toLocalDate().plusDays(5).toString(); // Use session target date

        // Mock the session repository
        when(sessionRepository.findById(sessionID)).thenReturn(Optional.of(resultSession));

        when(regionalWeatherRepository.findBySessionAndTargetTypeAndDayBefore(sessionID, "daily_zone", endDate))
                .thenReturn(List.of(regionalWeatherLatest, regionalWeatherLatest2));

        when(weatherAttributeResultDocumentMapper.mapRegionalWeatherDocuments(regionalWeatherDocuments))
                .thenReturn(expectedResult);

        List<WeatherAttributeResultDocument> result =
                testable.getAllResultsBySession(sessionID.toString(), null, "daily_zone");

        verify(regionalWeatherRepository, times(1))
                .findBySessionAndTargetTypeAndDayBefore(sessionID, "daily_zone", endDate);
        verify(weatherAttributeResultDocumentMapper, times(1)).mapRegionalWeatherDocuments(regionalWeatherDocuments);

        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetAllResultsBySession_ReturnsMappedResults_WhenParentZoneIsNotNull() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");
        ZonedDateTime sessionTargetDate = ZonedDateTime.parse("2020-10-28T12:00:00Z");

        // Mock the session
        ResultSession resultSession = ResultSession.builder()
                .id(sessionID)
                .targetType("daily_zone")
                .targetDate(sessionTargetDate)
                .build();

        RegionalWeather regionalWeatherLatest = RegionalWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("North America")
                        .displayZone("United States")
                        .day("2020-10-28")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(90.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        RegionalWeather regionalWeatherLatest2 = RegionalWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("North America")
                        .displayZone("United States")
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(40.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        WeatherAttributeResultDocument expectedFirstDocument = WeatherAttributeResultDocument.builder()
                .displayName("United States")
                .name("north-america")
                .day("2020-10-28")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(90.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        WeatherAttributeResultDocument expectedSecondDocument = WeatherAttributeResultDocument.builder()
                .displayName("United States")
                .name("north-america")
                .day("2020-10-29")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(40.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        List<RegionalWeather> regionalWeatherDocuments = List.of(regionalWeatherLatest, regionalWeatherLatest2);
        List<WeatherAttributeResultDocument> expectedResult = List.of(expectedFirstDocument, expectedSecondDocument);
        final String parentZone = "BIRMINGHAM";
        String endDate = sessionTargetDate.toLocalDate().plusDays(5).toString(); // Use session target date

        // Mock the session repository
        when(sessionRepository.findById(sessionID)).thenReturn(Optional.of(resultSession));

        when(regionalWeatherRepository.findBySessionAndParentZoneAndTargetTypeAndDayBefore(
                        sessionID, parentZone, "daily_zone", endDate))
                .thenReturn(List.of(regionalWeatherLatest, regionalWeatherLatest2));

        when(weatherAttributeResultDocumentMapper.mapRegionalWeatherDocuments(regionalWeatherDocuments))
                .thenReturn(expectedResult);

        List<WeatherAttributeResultDocument> result =
                testable.getAllResultsBySession(sessionID.toString(), parentZone, "daily_zone");

        verify(regionalWeatherRepository, times(1))
                .findBySessionAndParentZoneAndTargetTypeAndDayBefore(sessionID, parentZone, "daily_zone", endDate);
        verify(weatherAttributeResultDocumentMapper, times(1)).mapRegionalWeatherDocuments(regionalWeatherDocuments);

        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetLatestSessionNoData() {
        String targetType = "daily_zone";

        when(sessionRepository.findFirstByTypeOrderByCompletionDateDescOrThrow(targetType))
                .thenThrow(DomainObjectNotFoundException.class);

        assertThatThrownBy(() -> testable.getLatestSession(
                        LatestSessionRequestDto.builder().type(targetType).build()))
                .isInstanceOf(DomainObjectNotFoundException.class);
    }

    @Test
    void testGetAllResultsBySessionNoData() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");
        ZonedDateTime sessionTargetDate = ZonedDateTime.parse("2020-10-28T12:00:00Z");

        // Mock the session
        ResultSession resultSession = ResultSession.builder()
                .id(sessionID)
                .targetType("daily_zone")
                .targetDate(sessionTargetDate)
                .build();

        // Mock the session repository
        when(sessionRepository.findById(sessionID)).thenReturn(Optional.of(resultSession));

        when(regionalWeatherRepository.findBySessionAndTargetTypeAndDayBefore(
                        eq(sessionID), eq("daily_zone"), any(String.class)))
                .thenReturn(List.of());
        List<WeatherAttributeResultDocument> regionalWeatherList =
                testable.getAllResultsBySession(sessionID.toString(), null, "daily_zone");

        assertTrue(regionalWeatherList.isEmpty());
    }

    @Test
    void testGetLatestSessionByTargetTypeAndTargetDate() {
        // Mock the target date and the lookahead hours
        var targetDate = ZonedDateTime.now(UTC);
        int lookAheadHours = 2; // example value for lookahead hours
        ObjectId objectId = new ObjectId();
        // Create a mock result session object
        ResultSession resultSession = ResultSession.builder()
                .id(objectId)
                .targetDate(targetDate)
                .targetType("type")
                .build();

        // Mock the retrospective properties to return the lookahead hours
        when(retrospectiveProperties.getLookAheadHours()).thenReturn(lookAheadHours);

        // Adjust the target date with the lookahead and truncate to hours
        ZonedDateTime adjustedTargetDate = targetDate.plusHours(lookAheadHours).truncatedTo(ChronoUnit.HOURS);

        // Mock the repository method call with the adjusted target date
        when(sessionRepository.findByTargetTypeAndTargetDateOrThrow(eq("type"), eq(adjustedTargetDate)))
                .thenReturn(resultSession);

        // Build the request DTO
        var requestDto = LatestSessionRequestDto.builder()
                .type("type")
                .targetDate(targetDate)
                .build();

        // Call the service method
        String latestSessionResponse = testable.getLatestSession(requestDto);

        // Verify that the repository was called with the adjusted target date
        Mockito.verify(sessionRepository, times(1))
                .findByTargetTypeAndTargetDateOrThrow(eq("type"), eq(adjustedTargetDate));

        // Assert that the session and latest date are correct
        assertThat(objectId.toHexString()).isEqualTo(latestSessionResponse);
    }

    @Test
    void testGetAllResultsBySession_UsesSessionTargetDateForDateRange() {
        // Given
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");
        ZonedDateTime sessionTargetDate = ZonedDateTime.parse("2025-07-28T12:00:00Z");
        String expectedEndDate = "2025-08-02"; // sessionTargetDate + 5 days

        ResultSession resultSession = ResultSession.builder()
                .id(sessionID)
                .targetType("daily_zone")
                .targetDate(sessionTargetDate)
                .build();

        RegionalWeather regionalWeatherDoc = RegionalWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("North America")
                        .displayZone("United States")
                        .day("2025-07-28")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .build())
                        .build())
                .build();

        List<WeatherAttributeResultDocument> expectedResult = List.of(WeatherAttributeResultDocument.builder()
                .day("2025-07-28")
                .name("north-america")
                .displayName("United States")
                .build());

        // Mock the session repository to return the session with target date
        when(sessionRepository.findById(sessionID)).thenReturn(Optional.of(resultSession));

        // Mock the weather repository to return documents
        when(regionalWeatherRepository.findBySessionAndTargetTypeAndDayBefore(sessionID, "daily_zone", expectedEndDate))
                .thenReturn(List.of(regionalWeatherDoc));

        // Mock the mapper
        when(weatherAttributeResultDocumentMapper.mapRegionalWeatherDocuments(List.of(regionalWeatherDoc)))
                .thenReturn(expectedResult);

        // When
        List<WeatherAttributeResultDocument> result =
                testable.getAllResultsBySession(sessionID.toString(), null, "daily_zone");

        // Then
        verify(sessionRepository, times(1)).findById(sessionID);
        verify(regionalWeatherRepository, times(1))
                .findBySessionAndTargetTypeAndDayBefore(sessionID, "daily_zone", expectedEndDate);

        assertThat(result).hasSize(1);
        assertThat(result).isEqualTo(expectedResult);
    }
}

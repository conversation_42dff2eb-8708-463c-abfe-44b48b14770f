package com.esource.stormweatherservice.service.mapper;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.esource.stormweatherservice.model.RegionalWeather;
import com.esource.stormweatherservice.model.Result;
import com.esource.stormweatherservice.model.SystemResult;
import com.esource.stormweatherservice.model.SystemWeather;
import com.esource.stormweatherservice.model.WeatherAggregate;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = WeatherAttributeResultDocumentMapperImpl.class)
class WeatherAttributeResultDocumentMapperTest {

    @Autowired
    private WeatherAttributeResultDocumentMapper testable;

    @Test
    public void testMapSystemWeatherDocuments() {
        SystemWeather systemWeather1 = new SystemWeather();
        systemWeather1.setResults(generateSystemWeatherResults("2024-10-06"));

        SystemWeather systemWeather2 = new SystemWeather();
        systemWeather2.setResults(generateSystemWeatherResults("2024-10-07"));

        List<SystemWeather> systemWeathers = Arrays.asList(systemWeather1, systemWeather2);

        List<WeatherAttributeResultDocument> resultDocuments = testable.mapSystemWeatherDocuments(systemWeathers);

        assertThat(resultDocuments).hasSize(2);
        assertThat(resultDocuments.get(0).getName()).isEqualTo("System");
        assertThat(resultDocuments.get(1).getName()).isEqualTo("System");
    }

    @Test
    public void testMapRegionalWeatherDocuments() {
        RegionalWeather regionalWeather1 = new RegionalWeather();
        regionalWeather1.setResults(generateRegionalWeatherResults("North Region", "Zone A"));

        RegionalWeather regionalWeather2 = new RegionalWeather();
        regionalWeather2.setResults(generateRegionalWeatherResults("South Region", "Zone B"));

        List<RegionalWeather> regionalWeathers = Arrays.asList(regionalWeather1, regionalWeather2);

        List<WeatherAttributeResultDocument> resultDocuments = testable.mapRegionalWeatherDocuments(regionalWeathers);

        assertThat(resultDocuments).hasSize(2);
        assertThat(resultDocuments.get(0).getName()).isEqualTo("north-region");
        assertThat(resultDocuments.get(0).getDisplayName()).isEqualTo("Zone A");
        assertThat(resultDocuments.get(1).getName()).isEqualTo("south-region");
        assertThat(resultDocuments.get(1).getDisplayName()).isEqualTo("Zone B");
    }

    private SystemResult generateSystemWeatherResults(String day) {
        SystemResult results = new SystemResult();
        results.setDay(day);

        WeatherAggregate weatherAgg = new WeatherAggregate();
        weatherAgg.setPrecip(0.5);
        weatherAgg.setWindGustMax(15.0);
        weatherAgg.setWindSustained(10.0);
        weatherAgg.setTemperatureMinFahrenheit(32.0);
        weatherAgg.setTemperatureMeanFahrenheit(50.0);
        weatherAgg.setTemperatureMaxFahrenheit(70.0);
        weatherAgg.setThreatLevel(0.0);
        weatherAgg.setThreatLevelCategory("Category 1");
        weatherAgg.setCloudCoverPercentMean(50.0);

        results.setWeatherAgg(weatherAgg);
        return results;
    }

    private Result generateRegionalWeatherResults(String region, String displayZone) {
        Result results = new Result();
        results.setRegion(region);
        results.setDisplayZone(displayZone);

        WeatherAggregate weatherAgg = new WeatherAggregate();
        weatherAgg.setPrecip(0.2);
        weatherAgg.setWindGustMax(12.0);
        weatherAgg.setWindSustained(8.0);
        weatherAgg.setTemperatureMinFahrenheit(40.0);
        weatherAgg.setTemperatureMeanFahrenheit(60.0);
        weatherAgg.setTemperatureMaxFahrenheit(80.0);
        weatherAgg.setThreatLevel(1.0);
        weatherAgg.setThreatLevelCategory("Category 2");
        weatherAgg.setCloudCoverPercentMean(30.0);

        results.setWeatherAgg(weatherAgg);
        return results;
    }
}

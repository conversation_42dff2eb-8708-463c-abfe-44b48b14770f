package com.esource.stormweatherservice.service;

import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.esource.stormweatherservice.model.ResultSession;
import com.esource.stormweatherservice.model.SystemResult;
import com.esource.stormweatherservice.model.SystemWeather;
import com.esource.stormweatherservice.model.WeatherAggregate;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.repository.RegionalWeatherRepository;
import com.esource.stormweatherservice.repository.ResultSessionRepository;
import com.esource.stormweatherservice.repository.SystemWeatherDao;
import com.esource.stormweatherservice.repository.SystemWeatherRepository;
import com.esource.stormweatherservice.service.mapper.WeatherAttributeResultDocumentMapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = SystemWeatherService.class)
class SystemWeatherServiceTest {

    @MockBean
    private SystemWeatherRepository systemWeatherRepository;

    @MockBean
    private WeatherAttributeResultDocumentMapper weatherAttributeResultDocumentMapper;

    @MockBean
    private SystemWeatherDao systemWeatherDao;

    @MockBean
    private RegionalWeatherRepository regionalWeatherRepository;

    @MockBean
    private ResultSessionRepository resultSessionRepository;

    @Autowired
    private SystemWeatherService testable;

    @Test
    void testGetAllResultsBySession_ReturnsMappedResults_WhenParentZoneIsNull() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");

        SystemWeather systemWeatherLatest = SystemWeather.builder()
                .session(sessionID)
                .type("daily")
                .targetDate(new Date())
                .results(SystemResult.builder()
                        .day("2020-10-28")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(90.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        SystemWeather systemWeatherLatest2 = SystemWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(SystemResult.builder()
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(40.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        WeatherAttributeResultDocument expectedFirstDocument = WeatherAttributeResultDocument.builder()
                .displayName("System")
                .name("System")
                .day("2020-10-28")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(90.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        WeatherAttributeResultDocument expectedSecondDocument = WeatherAttributeResultDocument.builder()
                .displayName("System")
                .name("System")
                .day("2020-10-29")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(40.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        List<SystemWeather> systemWeatherDocuments = List.of(systemWeatherLatest, systemWeatherLatest2);
        List<WeatherAttributeResultDocument> expectedResult = List.of(expectedFirstDocument, expectedSecondDocument);

        when(systemWeatherRepository.findTop6BySessionOrderByTargetDateDesc(sessionID))
                .thenReturn(systemWeatherDocuments);

        when(weatherAttributeResultDocumentMapper.mapSystemWeatherDocuments(systemWeatherDocuments))
                .thenReturn(expectedResult);

        List<WeatherAttributeResultDocument> result = testable.getAllResultsBySession(sessionID.toString(), null);

        verify(systemWeatherRepository, times(1)).findTop6BySessionOrderByTargetDateDesc(sessionID);
        verify(weatherAttributeResultDocumentMapper, times(1)).mapSystemWeatherDocuments(systemWeatherDocuments);

        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetAllResultsBySession_ReturnsMappedResults_WhenParentZoneIsNotNull() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");

        SystemWeather systemWeatherLatest = SystemWeather.builder()
                .session(sessionID)
                .type("daily")
                .targetDate(new Date())
                .results(SystemResult.builder()
                        .day("2020-10-28")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(90.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        SystemWeather systemWeatherLatest2 = SystemWeather.builder()
                .session(sessionID)
                .type("daily_zone")
                .targetDate(new Date())
                .results(SystemResult.builder()
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .temperatureMin(2.0)
                                .temperatureMean(3.0)
                                .temperatureMax(4.0)
                                .temperatureMinFahrenheit(20.0)
                                .temperatureMeanFahrenheit(30.0)
                                .temperatureMaxFahrenheit(40.0)
                                .threatLevel(40.0)
                                .threatLevelCategory("Threat")
                                .cloudCoverPercentMean(98.9)
                                .build())
                        .build())
                .build();

        List<SystemWeather> systemWeatherDocuments = List.of(systemWeatherLatest, systemWeatherLatest2);

        WeatherAttributeResultDocument expectedFirstDocument = WeatherAttributeResultDocument.builder()
                .displayName("System")
                .name("System")
                .day("2020-10-28")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(90.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        WeatherAttributeResultDocument expectedSecondDocument = WeatherAttributeResultDocument.builder()
                .displayName("System")
                .name("System")
                .day("2020-10-29")
                .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .temperatureMin(20.0)
                        .temperatureMean(30.0)
                        .temperatureMax(40.0)
                        .threatLevel(40.0)
                        .threatLevelCategory("Threat")
                        .cloudCoverPercentMean(98.9)
                        .build())
                .build();

        final String parentZone = "BIRMINGHAM";

        List<WeatherAttributeResultDocument> expectedResult = List.of(expectedFirstDocument, expectedSecondDocument);

        when(systemWeatherDao.getPredictionsBySessionAndParentZoneOrderByTargetDateDesc(sessionID, parentZone))
                .thenReturn(systemWeatherDocuments);

        when(weatherAttributeResultDocumentMapper.mapSystemWeatherDocuments(systemWeatherDocuments))
                .thenReturn(expectedResult);

        List<WeatherAttributeResultDocument> result = testable.getAllResultsBySession(sessionID.toString(), parentZone);

        verify(systemWeatherDao, times(1))
                .getPredictionsBySessionAndParentZoneOrderByTargetDateDesc(sessionID, parentZone);
        verify(weatherAttributeResultDocumentMapper, times(1)).mapSystemWeatherDocuments(systemWeatherDocuments);
        verify(systemWeatherRepository, never()).findTop6BySessionOrderByTargetDateDesc(sessionID);

        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    void testGetAllResultsBySessionNoData() {
        var sessionID = new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c");

        when(systemWeatherRepository.findBySession(any(ObjectId.class), any(String.class)))
                .thenReturn(List.of());
        when(resultSessionRepository.findFirstByTypeOrderByCompletionDateDescOrThrow(any(String.class)))
                .thenReturn(ResultSession.builder().id(new ObjectId()).build());
        List<WeatherAttributeResultDocument> regionalWeatherList =
                testable.getAllResultsBySession(sessionID.toString(), null);

        assertThat(regionalWeatherList).isEmpty();
    }
}

package com.esource.stormweatherservice.integration;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.testcontainers.shaded.com.fasterxml.jackson.core.type.TypeReference;
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper;

import com.esource.stormweatherservice.helpers.AbstractTestContainer;
import com.esource.stormweatherservice.model.*;
import com.esource.stormweatherservice.repository.RegionalWeatherRepository;
import com.esource.stormweatherservice.repository.ResultSessionRepository;

class BaseControllerIntegrationTests extends AbstractTestContainer {

    @Autowired
    private ResultSessionRepository resultSessionRepository;

    @Autowired
    private RegionalWeatherRepository regionalWeatherRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @AfterEach
    void tearDown() {
        resultSessionRepository.deleteAll();
        regionalWeatherRepository.deleteAll();
    }

    @Test
    @WithMockUser(value = "user")
    void testGetLatestSession() throws Exception {

        // Given
        resultSessionRepository.saveAll(List.of(
                ResultSession.builder()
                        .id(new ObjectId("647a3c72ecdc2f200424e3a3"))
                        .targetType("daily_zone")
                        .targetDate(ZonedDateTime.parse("2020-01-01T00:00:00.000Z"))
                        .completionDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0))
                        .build(),
                ResultSession.builder()
                        .id(new ObjectId("647a3c72ecdc2f200424e3a4"))
                        .targetType("daily_zone")
                        .targetDate(ZonedDateTime.parse("2020-01-02T00:00:00.000Z"))
                        .completionDate(LocalDateTime.of(2020, 1, 2, 0, 0, 0, 0))
                        .build()));

        // Perform the request using MockMvc
        // When
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/weatherAttributes/sessions/latest")
                        .param("type", "daily_zone"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();

        // Then
        Assertions.assertEquals("647a3c72ecdc2f200424e3a4", response);
    }

    @Test
    @WithMockUser(value = "user")
    void testGetWeatherAttributesBySession() throws Exception {
        List<RegionalWeather> regionalWeatherList =
                buildRegionalWeatherByDays(6, new ObjectId("647a3c72ecdc2f200424e3a4"));
        regionalWeatherRepository.saveAll(regionalWeatherList);

        MvcResult mvcResult = mockMvc.perform(
                        MockMvcRequestBuilders.get("/weatherAttributes/sessions/647a3c72ecdc2f200424e3a4"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();

        List<WeatherAttributeResultDocument> actualWeatherList =
                objectMapper.readValue(response, new TypeReference<List<WeatherAttributeResultDocument>>() {});

        // Then
        Assertions.assertEquals(buildWeatherAttributeResultDocumentExpectedByDays(5), actualWeatherList);
    }

    private List<RegionalWeather> buildRegionalWeatherByDays(int days, ObjectId sessionId) {
        List<RegionalWeather> weatherList = new ArrayList<>();

        for (int i = 0; i < days; i++) {
            LocalDateTime now = LocalDateTime.now().plusDays(i);
            weatherList.add(RegionalWeather.builder()
                    .session(sessionId)
                    .type("daily_zone")
                    .results(Result.builder()
                            .day(String.format(
                                    "%04d-%02d-%02d",
                                    now.getYear(), now.getMonth().getValue(), now.getDayOfMonth()))
                            .region("North Central")
                            .displayZone("North Central")
                            .weatherAgg(WeatherAggregate.builder()
                                    .precip(1.0)
                                    .windGustMax(2.0)
                                    .windSustained(3.0)
                                    .temperatureMin(4.0)
                                    .temperatureMean(5.0)
                                    .temperatureMax(6.0)
                                    .temperatureMinFahrenheit(7.0)
                                    .temperatureMeanFahrenheit(8.0)
                                    .temperatureMaxFahrenheit(9.0)
                                    .threatLevel(10.0)
                                    .build())
                            .build())
                    .build());
        }

        return weatherList;
    }

    private List<WeatherAttributeResultDocument> buildWeatherAttributeResultDocumentExpectedByDays(int days) {
        List<WeatherAttributeResultDocument> weatherList = new ArrayList<>();

        for (int i = 0; i < days; i++) {
            LocalDateTime now = LocalDateTime.now().plusDays(i);
            weatherList.add(WeatherAttributeResultDocument.builder()
                    .name("north-central")
                    .displayName("North Central")
                    .day(String.format(
                            "%04d-%02d-%02d", now.getYear(), now.getMonth().getValue(), now.getDayOfMonth()))
                    .weatherAttributes(WeatherAttributeResultDocument.WeatherAttributes.builder()
                            .precip(1.0)
                            .windGustMax(2.0)
                            .windSustained(3.0)
                            .temperatureMin(7.0)
                            .temperatureMean(8.0)
                            .temperatureMax(9.0)
                            .threatLevel(10.0)
                            .build())
                    .build());
        }

        return weatherList;
    }
}

package com.esource.stormweatherservice.integration;

import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import com.esource.stormweatherservice.helpers.AbstractTestContainer;
import com.esource.stormweatherservice.model.*;
import com.esource.stormweatherservice.repository.ConfigurationRepository;

import static com.esource.stormweatherservice.http.controller.ChartConfigController.CHART_TYPE;

public class ChartConfigControllerIntegrationTests extends AbstractTestContainer {
    @Autowired
    private ConfigurationRepository configurationRepository;

    @AfterEach
    void tearDown() {
        configurationRepository.deleteAll();
    }

    @Test
    @WithMockUser(value = "user")
    void testChartConfigs() throws Exception {
        Configurations configuration = Configurations.builder()
                .id(new ObjectId("647a3c72ecdc2f200424e3a3"))
                .type(CHART_TYPE)
                .configs(List.of(Config.builder()
                        .type("config_type")
                        .isDisplayed(true)
                        .isDefaultDisplay(true)
                        .displayName("display_name")
                        .attributeName("attribute_name")
                        .yAxis(YAxis.builder()
                                .range(Range.builder().min(0.0).max(100.0).build())
                                .unit("unit")
                                .build())
                        .build()))
                .build();

        configurationRepository.save(configuration);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/chartConfigs"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();

        // Then
        Assertions.assertEquals(
                "[" + "{"
                        + "\"id\":{\"timestamp\":1685732466,\"date\":\"2023-06-02T19:01:06.000+00:00\"},"
                        + "\"type\":\"chart\","
                        + "\"configs\":["
                        + "{"
                        + "\"type\":\"config_type\","
                        + "\"isDisplayed\":true,"
                        + "\"isDefaultDisplay\":true,"
                        + "\"displayName\":\"display_name\","
                        + "\"attributeName\":\"attribute_name\","
                        + "\"yaxis\":{\"range\":{\"min\":0.0,\"max\":100.0},\"unit\":\"unit\"}}]}]",
                response);
    }

    @Test
    @WithMockUser(value = "user")
    void testChartConfigsWithConfigTypeFilter() throws Exception {
        Configurations configuration = Configurations.builder()
                .id(new ObjectId("647a3c72ecdc2f200424e3a3"))
                .type(CHART_TYPE)
                .configs(List.of(
                        Config.builder()
                                .type("config_type")
                                .isDisplayed(true)
                                .isDefaultDisplay(true)
                                .displayName("display_name")
                                .attributeName("attribute_name")
                                .yAxis(YAxis.builder()
                                        .range(Range.builder()
                                                .min(0.0)
                                                .max(100.0)
                                                .build())
                                        .unit("unit")
                                        .build())
                                .build(),
                        Config.builder()
                                .type("config_type_to_be_filtered_out")
                                .isDisplayed(true)
                                .isDefaultDisplay(true)
                                .displayName("display_name")
                                .attributeName("attribute_name")
                                .yAxis(YAxis.builder()
                                        .range(Range.builder()
                                                .min(0.0)
                                                .max(100.0)
                                                .build())
                                        .unit("unit")
                                        .build())
                                .build()))
                .build();

        configurationRepository.save(configuration);
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.get("/chartConfigs/config_type"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn();

        String response = mvcResult.getResponse().getContentAsString();

        // Then
        Assertions.assertEquals(
                "{" + "\"id\":{\"timestamp\":1685732466,\"date\":\"2023-06-02T19:01:06.000+00:00\"},"
                        + "\"type\":\"chart\","
                        + "\"configs\":["
                        + "{"
                        + "\"type\":\"config_type\","
                        + "\"isDisplayed\":true,"
                        + "\"isDefaultDisplay\":true,"
                        + "\"displayName\":\"display_name\","
                        + "\"attributeName\":\"attribute_name\","
                        + "\"yaxis\":{\"range\":{\"min\":0.0,\"max\":100.0},\"unit\":\"unit\"}}]}",
                response);
    }
}

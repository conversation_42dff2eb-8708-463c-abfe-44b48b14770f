package com.esource.stormweatherservice.model;

import java.util.Date;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class RegionalWeatherTest {

    @Test
    void testBuild() {
        RegionalWeather regionalWeather = RegionalWeather.builder()
                .session(new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c"))
                .type("type")
                .targetDate(new Date())
                .results(Result.builder()
                        .region("US")
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .build())
                        .build())
                .build();

        assertNotNull(regionalWeather);
        assertEquals("5f9f1b9b4b6b8b0b8c8c8c8c", regionalWeather.getSession().toString());
        assertEquals("type", regionalWeather.getType());
        assertNotNull(regionalWeather.getTargetDate());
        assertEquals("US", regionalWeather.getResults().getRegion());
        assertEquals("2020-10-29", regionalWeather.getResults().getDay());
        assertEquals(0.0, regionalWeather.getResults().getWeatherAgg().getPrecip());
        assertEquals(3.24, regionalWeather.getResults().getWeatherAgg().getWindGustMax());
        assertEquals(1.62, regionalWeather.getResults().getWeatherAgg().getWindSustained());
    }

    @Test
    void testAllArgsConstructor() {
        RegionalWeather regionalWeather = new RegionalWeather(
                "2",
                "type",
                new Date(),
                new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c"),
                Result.builder()
                        .region("US")
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .build())
                        .build());

        assertNotNull(regionalWeather);
        assertEquals("5f9f1b9b4b6b8b0b8c8c8c8c", regionalWeather.getSession().toString());
        assertEquals("type", regionalWeather.getType());
        assertNotNull(regionalWeather.getTargetDate());
        assertEquals("US", regionalWeather.getResults().getRegion());
        assertEquals("2020-10-29", regionalWeather.getResults().getDay());
        assertEquals(0.0, regionalWeather.getResults().getWeatherAgg().getPrecip());
        assertEquals(3.24, regionalWeather.getResults().getWeatherAgg().getWindGustMax());
        assertEquals(1.62, regionalWeather.getResults().getWeatherAgg().getWindSustained());
    }

    @Test
    void testDataAnnotation() {
        assertNotNull(
                RegionalWeather.class.getAnnotation(org.springframework.data.mongodb.core.mapping.Document.class));
    }

    @Test
    void testDataMethods() {
        RegionalWeather regionalWeather = RegionalWeather.builder()
                .session(new ObjectId("5f9f1b9b4b6b8b0b8c8c8c8c"))
                .type("type")
                .results(Result.builder()
                        .region("US")
                        .displayZone("United States")
                        .day("2020-10-29")
                        .weatherAgg(WeatherAggregate.builder()
                                .precip(0.0)
                                .windGustMax(3.24)
                                .windSustained(1.62)
                                .build())
                        .build())
                .build();

        assertNotNull(regionalWeather.toString());
        assertEquals(true, regionalWeather.equals(regionalWeather));
        assertEquals(false, regionalWeather.equals(null));
        assertEquals(false, regionalWeather.equals(new Object()));
    }

    @Test
    void testResultBuilder() {
        Result result = Result.builder()
                .region("US")
                .day("2020-10-29")
                .displayZone("United States")
                .weatherAgg(WeatherAggregate.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .build())
                .build();

        assertNotNull(result);
        assertEquals("US", result.getRegion());
        assertEquals("2020-10-29", result.getDay());
        assertEquals(0.0, result.getWeatherAgg().getPrecip());
        assertEquals(3.24, result.getWeatherAgg().getWindGustMax());
        assertEquals(1.62, result.getWeatherAgg().getWindSustained());
    }

    @Test
    void testResultToStringAndHashCode() {
        Result result = Result.builder()
                .parentZone("BIRMINGHAM")
                .region("US")
                .displayZone("United States")
                .day("2020-10-29")
                .weatherAgg(WeatherAggregate.builder()
                        .precip(0.0)
                        .windGustMax(3.24)
                        .windSustained(1.62)
                        .build())
                .build();

        assertNotNull(result.toString());
        assertEquals(
                "Result(day=2020-10-29, parentZone=BIRMINGHAM, region=US, displayZone=United States, generationTime=null, weatherAgg=WeatherAggregate(precip=0.0, windGustMax=3.24, windSustained=1.62, temperatureMin=null, temperatureMean=null, temperatureMax=null, temperatureMinFahrenheit=null, temperatureMeanFahrenheit=null, temperatureMaxFahrenheit=null, threatLevel=null, threatLevelCategory=null, cloudCoverPercentMean=null))",
                result.toString());
    }

    @Test
    void testWeatherAggregateBuilder() {
        WeatherAggregate weatherAggregate = WeatherAggregate.builder()
                .precip(0.0)
                .windGustMax(3.24)
                .windSustained(1.62)
                .build();

        assertNotNull(weatherAggregate);
        assertEquals(0.0, weatherAggregate.getPrecip());
        assertEquals(3.24, weatherAggregate.getWindGustMax());
        assertEquals(1.62, weatherAggregate.getWindSustained());
    }

    @Test
    void testMissingResultsBuilder() {
        Result missingResults = Result.builder().region("US").build();

        assertNotNull(missingResults);
        assertEquals("US", missingResults.getRegion());
        assertEquals(null, missingResults.getDay());
    }

    @Test
    void testWeatherAggregateGettersAndSetters() {
        WeatherAggregate weatherAggregate =
                new WeatherAggregate(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 20.0, 30.0, 40.0, 9.0, "Threat", 98.9);
        weatherAggregate.setPrecip(0.0);
        weatherAggregate.setWindGustMax(3.24);
        weatherAggregate.setWindSustained(1.62);
        weatherAggregate.setTemperatureMin(3.0);
        weatherAggregate.setTemperatureMean(6.0);
        weatherAggregate.setTemperatureMax(9.0);
        weatherAggregate.setTemperatureMinFahrenheit(37.0);
        weatherAggregate.setTemperatureMeanFahrenheit(37.0);
        weatherAggregate.setTemperatureMaxFahrenheit(37.0);
        weatherAggregate.setCloudCoverPercentMean(98.9);
        weatherAggregate.setTemperatureMaxFahrenheit(37.0);
        weatherAggregate.setThreatLevel(9.0);
        weatherAggregate.setThreatLevelCategory("Threat");

        assertNotNull(weatherAggregate);
        assertEquals(0.0, weatherAggregate.getPrecip());
        assertEquals(3.24, weatherAggregate.getWindGustMax());
        assertEquals(1.62, weatherAggregate.getWindSustained());
        assertEquals(3, weatherAggregate.getTemperatureMin());
        assertEquals(6, weatherAggregate.getTemperatureMean());
        assertEquals(9, weatherAggregate.getTemperatureMax());
        assertEquals(37, weatherAggregate.getTemperatureMinFahrenheit());
        assertEquals(37, weatherAggregate.getTemperatureMeanFahrenheit());
        assertEquals(37, weatherAggregate.getTemperatureMaxFahrenheit());
        assertEquals(98.9, weatherAggregate.getCloudCoverPercentMean());
        assertEquals(9.0, weatherAggregate.getThreatLevel());
        assertEquals("Threat", weatherAggregate.getThreatLevelCategory());
    }
}

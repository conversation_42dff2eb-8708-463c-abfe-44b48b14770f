package com.esource.stormweatherservice.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

class WeatherAttributeResultDocumentMapperTest {

    @Test
    void testReturnObjectBuilder() {
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);

        WeatherAttributeResultDocument returnObject = WeatherAttributeResultDocument.builder()
                .name("north-america")
                .day("2020-10-29")
                .displayName("North America")
                .weatherAttributes(weatherAttributes)
                .build();

        assertEquals("north-america", returnObject.getName());
        assertEquals("2020-10-29", returnObject.getDay());
        assertEquals("North America", returnObject.getDisplayName());
        assertEquals(weatherAttributes, returnObject.getWeatherAttributes());
    }

    @Test
    void testWeatherAttributesAllArgsConstructor() {
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);

        assertEquals(0.5, weatherAttributes.getPrecip(), 0.0);
        assertEquals(3.24, weatherAttributes.getWindGustMax(), 0.0);
        assertEquals(12, weatherAttributes.getWindSustained(), 0.0);
        assertEquals(0, weatherAttributes.getTemperatureMin(), 0.0);
        assertEquals(3, weatherAttributes.getTemperatureMean(), 0.0);
        assertEquals(6, weatherAttributes.getTemperatureMax(), 0.0);
        assertEquals(9, weatherAttributes.getThreatLevel(), 0.0);
        assertEquals("Threat", weatherAttributes.getThreatLevelCategory());
        assertEquals(98.9, weatherAttributes.getCloudCoverPercentMean(), 0.0);
    }

    @Test
    void testReturnObjectAllArgsConstructor() {
        WeatherAttributeResultDocument returnObject = new WeatherAttributeResultDocument(
                "north-america",
                "2020-10-29",
                "North America",
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9));

        assertEquals("north-america", returnObject.getName());
        assertEquals("2020-10-29", returnObject.getDay());
        assertEquals("North America", returnObject.getDisplayName());
        assertEquals(0.5, returnObject.getWeatherAttributes().getPrecip(), 0.0);
        assertEquals(3.24, returnObject.getWeatherAttributes().getWindGustMax(), 0.0);
        assertEquals(12, returnObject.getWeatherAttributes().getWindSustained(), 0.0);
        assertEquals(9, returnObject.getWeatherAttributes().getThreatLevel(), 0.0);
        assertEquals("Threat", returnObject.getWeatherAttributes().getThreatLevelCategory());
        assertEquals(98.9, returnObject.getWeatherAttributes().getCloudCoverPercentMean(), 0.0);
    }

    @Test
    void testReturnObjectNoArgsConstructor() {
        WeatherAttributeResultDocument returnObject = new WeatherAttributeResultDocument();

        assertNull(returnObject.getName());
        assertNull(returnObject.getDay());
        assertNull(returnObject.getDisplayName());
        assertNull(returnObject.getWeatherAttributes());
        Assertions.assertNotNull(returnObject);
    }

    @Test
    void testReturnObjectGettersAndSetters() {
        WeatherAttributeResultDocument returnObject = new WeatherAttributeResultDocument();
        returnObject.setName("north-america");
        returnObject.setDay("2020-10-29");
        returnObject.setDisplayName("North America");
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);
        returnObject.setWeatherAttributes(weatherAttributes);

        assertEquals("north-america", returnObject.getName());
        assertEquals("2020-10-29", returnObject.getDay());
        assertEquals("North America", returnObject.getDisplayName());
        assertEquals(weatherAttributes, returnObject.getWeatherAttributes());
    }

    @Test
    void testWeatherAttributesGettersAndSetters() {
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);

        assertEquals(0.5, weatherAttributes.getPrecip());
        assertEquals(3.24, weatherAttributes.getWindGustMax());
        assertEquals(12, weatherAttributes.getWindSustained());
        assertEquals(0, weatherAttributes.getTemperatureMin());
        assertEquals(3, weatherAttributes.getTemperatureMean());
        assertEquals(6, weatherAttributes.getTemperatureMax());
        assertEquals(98.9, weatherAttributes.getCloudCoverPercentMean());
        assertEquals("Threat", weatherAttributes.getThreatLevelCategory());
    }

    @Test
    void testReturnObjectEqualsAndHashCode() {
        WeatherAttributeResultDocument returnObject1 = new WeatherAttributeResultDocument();
        returnObject1.setName("north-america");
        returnObject1.setDay("2020-10-29");
        returnObject1.setDisplayName("North America");
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes1 =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);
        returnObject1.setWeatherAttributes(weatherAttributes1);

        WeatherAttributeResultDocument returnObject2 = new WeatherAttributeResultDocument();
        returnObject2.setName("north-america");
        returnObject2.setDay("2020-10-29");
        returnObject2.setDisplayName("North America");
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes2 =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);
        returnObject2.setWeatherAttributes(weatherAttributes2);

        assertEquals(returnObject1, returnObject2);
        assertEquals(returnObject1.hashCode(), returnObject2.hashCode());
    }

    @Test
    void testReturnObjectToString() {
        WeatherAttributeResultDocument returnObject = new WeatherAttributeResultDocument();
        returnObject.setName("north-america");
        returnObject.setDay("2020-10-29");
        returnObject.setDisplayName("North America");
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);
        returnObject.setWeatherAttributes(weatherAttributes);

        assertEquals(
                "WeatherAttributeResultDocument(name=north-america, day=2020-10-29, displayName=North America, weatherAttributes=WeatherAttributeResultDocument.WeatherAttributes(precip=0.5, windGustMax=3.24, windSustained=12.0, temperatureMin=0.0, temperatureMean=3.0, temperatureMax=6.0, threatLevel=9.0, threatLevelCategory=Threat, cloudCoverPercentMean=98.9))",
                returnObject.toString());
    }

    @Test
    void testWeatherAttributesEqualsAndHashCode() {
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes1 =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes2 =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);

        assertEquals(weatherAttributes1, weatherAttributes2);
        assertEquals(weatherAttributes1.hashCode(), weatherAttributes2.hashCode());
    }

    @Test
    void testWeatherAttributesToString() {
        WeatherAttributeResultDocument.WeatherAttributes weatherAttributes =
                new WeatherAttributeResultDocument.WeatherAttributes(
                        0.5, 3.24, 12.0, 0.0, 3.0, 6.0, 9.0, "Threat", 98.9);

        assertEquals(
                "WeatherAttributeResultDocument.WeatherAttributes(precip=0.5, windGustMax=3.24, windSustained=12.0, temperatureMin=0.0, temperatureMean=3.0, temperatureMax=6.0, threatLevel=9.0, threatLevelCategory=Threat, cloudCoverPercentMean=98.9)",
                weatherAttributes.toString());
    }

    @Test
    void testReturnObjectMissingArguments() {
        WeatherAttributeResultDocument returnObject = WeatherAttributeResultDocument.builder()
                .name("north-america")
                .day("2020-10-29")
                .displayName("North America")
                .build();

        assertEquals("north-america", returnObject.getName());
        assertEquals("2020-10-29", returnObject.getDay());
        assertEquals("North America", returnObject.getDisplayName());
        assertEquals(null, returnObject.getWeatherAttributes());
    }

    @Test
    void testReturnObjectAllMissingArguments() {
        WeatherAttributeResultDocument returnObject =
                WeatherAttributeResultDocument.builder().build();

        assertEquals(null, returnObject.getName());
        assertEquals(null, returnObject.getDay());
        assertEquals(null, returnObject.getDisplayName());
        assertEquals(null, returnObject.getWeatherAttributes());
    }
}

package com.esource.stormweatherservice.controller;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.esource.stormweatherservice.http.controller.ChartConfigController;
import com.esource.stormweatherservice.model.Configurations;
import com.esource.stormweatherservice.service.ConfigurationService;

import static com.esource.stormweatherservice.http.controller.ChartConfigController.CHART_TYPE;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ChartConfigControllerTest {

    @Mock
    private ConfigurationService configurationService;

    @InjectMocks
    private ChartConfigController controller;

    @BeforeEach
    void setUp() {
        configurationService = mock(ConfigurationService.class);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetConfigs() {
        // Given
        String targetType = "weather";

        Configurations configs = Configurations.builder().type(CHART_TYPE).build();
        when(configurationService.getConfigurationsByTypeAndConfigType(CHART_TYPE, targetType))
                .thenReturn(List.of(configs));

        // When
        ResponseEntity<Configurations> response = controller.getConfigs(targetType);

        // Then
        Assertions.assertEquals(configs, response.getBody());
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void testGetConfigsWhenEmptyListIsReturned() {
        // Given
        String targetType = "weather";

        when(configurationService.getConfigurationsByTypeAndConfigType(CHART_TYPE, targetType))
                .thenReturn(List.of());

        // When
        ResponseEntity<Configurations> response = controller.getConfigs(targetType);

        // Then
        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }

    @Test
    void testGetConfigsIfTypeIsNull() {
        // Given
        Configurations configs = Configurations.builder().type(CHART_TYPE).build();

        // When
        ResponseEntity<Configurations> response = controller.getConfigs(null);

        // Then
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void testGetConfigsIfTypeIsEmpty() {
        // Given
        Configurations configs = Configurations.builder().type(CHART_TYPE).build();

        // When
        ResponseEntity<Configurations> response = controller.getConfigs("");

        // Then
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void testGetAllChartConfigs() {
        // Given
        String targetType = "weather";

        Configurations configs = Configurations.builder().type(CHART_TYPE).build();
        when(configurationService.getAllConfigurationsByType(CHART_TYPE)).thenReturn(List.of(configs));

        // When
        ResponseEntity<List<Configurations>> response = controller.getAllChartConfigs();

        // Then
        Assertions.assertEquals(1, response.getBody().size());
        Assertions.assertEquals(configs, response.getBody().get(0));
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
    }
}

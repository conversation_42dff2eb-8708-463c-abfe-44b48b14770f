package com.esource.stormweatherservice.controller;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.esource.stormweatherservice.http.controller.RegionalWeatherController;
import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument.WeatherAttributes;
import com.esource.stormweatherservice.service.RegionalWeatherService;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class RegionalWeatherControllerTest {

    private MockMvc mvc;

    @Autowired
    private RegionalWeatherService regionalWeatherService;

    @Autowired
    private RegionalWeatherController controller;

    @BeforeEach
    void setUp() {
        regionalWeatherService = mock(RegionalWeatherService.class);
        controller = new RegionalWeatherController(regionalWeatherService);
        mvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void testGetLatestSessionByType() throws Exception {
        String expectedSessionId = "5f9f1b9b4b6b8b0b8c8c8c8c";
        String sessionType = "daily_zone";
        when(regionalWeatherService.getLatestSession(
                        LatestSessionRequestDto.builder().type("daily_zone").build()))
                .thenReturn(expectedSessionId);
        mvc.perform(get("/weather/search/latest-session/{sessionType}", sessionType)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));
    }

    @Test
    void testGetLatestSessionByType2() throws Exception {
        String expectedSessionId = "5f9f1b9b4b6b8b0b8c8c8c8c";

        when(regionalWeatherService.getLatestSession(
                        LatestSessionRequestDto.builder().type("daily_zone").build()))
                .thenReturn(expectedSessionId);

        mvc.perform(get("/weather/search/latest-session/daily_zone"))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));
    }

    @Test
    void testGetDocumentsBySession() throws Exception {
        WeatherAttributes weatherAttributes =
                new WeatherAttributes(2.0, 4.5, 6.0, 20.0, 30.0, 40.0, 9.0, "Threat", 98.9);

        WeatherAttributeResultDocument returnObjectOne = new WeatherAttributeResultDocument();
        returnObjectOne.setName("testName");
        returnObjectOne.setDisplayName("displayNameTest");
        returnObjectOne.setDay("2020-10-30");

        returnObjectOne.setWeatherAttributes(weatherAttributes);

        List<WeatherAttributeResultDocument> returnObjects = new ArrayList<>();
        returnObjects.add(returnObjectOne);

        when(regionalWeatherService.getAllResultsBySession("5f9f1b9b4b6b8b0b8c8c8c8c", null, "daily_zone"))
                .thenReturn(returnObjects);

        mvc.perform(get("/weather/search/session/5f9f1b9b4b6b8b0b8c8c8c8c"))
                .andExpect(status().isOk())
                .andExpect(
                        content()
                                .json(
                                        "[{\"name\":\"testName\",\"displayName\":\"displayNameTest\",\"day\":\"2020-10-30\",\"weatherAttributes\":{\"precip\":2.0,\"windGustMax\":4.5,\"windSustained\":6.0,\"temperatureMin\":20.0,\"temperatureMean\":30.0,\"temperatureMax\":40.0,\"threatLevel\":9.0,\"threatLevelCategory\":\"Threat\",\"cloudCoverPercentMean\":98.9}}]"));
    }
}

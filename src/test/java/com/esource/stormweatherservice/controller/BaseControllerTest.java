package com.esource.stormweatherservice.controller;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;

import com.esource.stormweatherservice.http.controller.BaseController;
import com.esource.stormweatherservice.http.dto.LatestSessionRequestDto;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument;
import com.esource.stormweatherservice.model.WeatherAttributeResultDocument.WeatherAttributes;
import com.esource.stormweatherservice.service.RegionalWeatherService;
import com.esource.stormweatherservice.service.SystemWeatherService;
import com.esource.stormweatherservice.service.exception.DomainObjectNotFoundException;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(BaseController.class)
class BaseControllerTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private RegionalWeatherService regionalWeatherService;

    @MockBean
    private SystemWeatherService systemWeatherService;

    @Autowired
    private ObjectMapper objectMapper;

    private final String expectedSessionId = "5f9f1b9b4b6b8b0b8c8c8c8c";
    private final String dailyZoneType = "daily_zone";
    private final String dailyParentZoneType = "daily_parent_zone";
    private final String dailyType = "daily";

    private WeatherAttributeResultDocument createSampleDoc(String name, String day) {
        return WeatherAttributeResultDocument.builder()
                .name(name.toLowerCase().replace(" ", "-"))
                .displayName(name + " Display")
                .day(day)
                .weatherAttributes(new WeatherAttributes(1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, "Cat", 8.0))
                .build();
    }

    @WithMockUser
    @Test
    void testGetLatestSessionByType() throws Exception {
        String sessionType = "daily_zone";
        when(regionalWeatherService.getLatestSession(
                        LatestSessionRequestDto.builder().type("daily_zone").build()))
                .thenReturn(expectedSessionId);
        mvc.perform(get("/weatherAttributes/sessions/latest")
                        .param("type", sessionType)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));
    }

    @WithMockUser
    @Test
    void testGetLatestSessionByType_DailyZone() throws Exception {
        LatestSessionRequestDto expectedDto =
                LatestSessionRequestDto.builder().type(dailyZoneType).build();
        when(regionalWeatherService.getLatestSession(expectedDto)).thenReturn(expectedSessionId);

        mvc.perform(get("/weatherAttributes/sessions/latest")
                        .param("type", dailyZoneType)
                        .accept(MediaType.TEXT_PLAIN))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));

        verify(regionalWeatherService).getLatestSession(expectedDto);
    }

    @WithMockUser
    @Test
    void testGetLatestSessionByType_DailyParentZone() throws Exception {
        LatestSessionRequestDto expectedDto =
                LatestSessionRequestDto.builder().type(dailyParentZoneType).build();
        when(regionalWeatherService.getLatestSession(expectedDto)).thenReturn(expectedSessionId);

        mvc.perform(get("/weatherAttributes/sessions/latest")
                        .param("type", dailyParentZoneType)
                        .accept(MediaType.TEXT_PLAIN))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));

        verify(regionalWeatherService).getLatestSession(expectedDto);
    }

    @WithMockUser
    @Test
    void testGetDocumentsBySession_DailyParentZone_WithParentZoneParam() throws Exception {
        String parentZone = "PARENT_Y";
        List<WeatherAttributeResultDocument> expectedDocs = List.of(createSampleDoc("parentYPlace", "2023-01-05"));
        when(regionalWeatherService.getAllResultsBySession(
                        eq(expectedSessionId), eq(parentZone), eq(dailyParentZoneType)))
                .thenReturn(expectedDocs);

        ResultActions result = mvc.perform(get("/weatherAttributes/sessions/{session}", expectedSessionId)
                .param("targetType", dailyParentZoneType)
                .param("parentZone", parentZone)
                .accept(MediaType.APPLICATION_JSON));

        result.andExpect(status().isOk()).andExpect(content().json(objectMapper.writeValueAsString(expectedDocs)));

        verify(regionalWeatherService)
                .getAllResultsBySession(eq(expectedSessionId), eq(parentZone), eq(dailyParentZoneType));
    }

    @WithMockUser
    @Test
    void testGetLatestSessionByType2() throws Exception {
        when(regionalWeatherService.getLatestSession(
                        LatestSessionRequestDto.builder().type("daily_zone").build()))
                .thenReturn(expectedSessionId);

        mvc.perform(get("/weatherAttributes/sessions/latest").param("type", "daily_zone"))
                .andExpect(status().isOk())
                .andExpect(content().string(expectedSessionId));
    }

    @WithMockUser
    @Test
    void testGetDocumentsBySession() throws Exception {
        WeatherAttributes weatherAttributes =
                new WeatherAttributes(2.0, 4.5, 6.0, 20.0, 30.0, 40.0, 9.0, "Threat", 98.9);

        WeatherAttributeResultDocument returnObjectOne = new WeatherAttributeResultDocument();
        returnObjectOne.setName("testName");
        returnObjectOne.setDisplayName("displayNameTest");
        returnObjectOne.setDay("2020-10-30");

        returnObjectOne.setWeatherAttributes(weatherAttributes);

        List<WeatherAttributeResultDocument> returnObjects = new ArrayList<>();
        returnObjects.add(returnObjectOne);

        when(regionalWeatherService.getAllResultsBySession("5f9f1b9b4b6b8b0b8c8c8c8c", null, "daily_zone"))
                .thenReturn(returnObjects);

        mvc.perform(get("/weatherAttributes/sessions/5f9f1b9b4b6b8b0b8c8c8c8c"))
                .andExpect(status().isOk())
                .andExpect(
                        content()
                                .json(
                                        "[{\"name\":\"testName\",\"displayName\":\"displayNameTest\",\"day\":\"2020-10-30\",\"weatherAttributes\":{\"precip\":2.0,\"windGustMax\":4.5,\"windSustained\":6.0,\"temperatureMin\":20.0,\"temperatureMean\":30.0,\"temperatureMax\":40.0,\"threatLevel\":9.0,\"threatLevelCategory\":\"Threat\",\"cloudCoverPercentMean\":98.9}}]"));
    }

    @WithMockUser
    @Test
    void testGetDocumentsBySessionByDailyType() throws Exception {
        WeatherAttributes weatherAttributes =
                new WeatherAttributes(2.0, 4.5, 6.0, 20.0, 30.0, 40.0, 9.0, "Threat", 98.9);

        WeatherAttributeResultDocument returnObjectOne = new WeatherAttributeResultDocument();
        returnObjectOne.setName("System");
        returnObjectOne.setDisplayName("System");
        returnObjectOne.setDay("2020-10-30");

        returnObjectOne.setWeatherAttributes(weatherAttributes);

        List<WeatherAttributeResultDocument> returnObjects = new ArrayList<>();
        returnObjects.add(returnObjectOne);

        when(systemWeatherService.getAllResultsBySession("5f9f1b9b4b6b8b0b8c8c8c8c", null))
                .thenReturn(returnObjects);

        mvc.perform(get("/weatherAttributes/sessions/5f9f1b9b4b6b8b0b8c8c8c8c?targetType=daily"))
                .andExpect(status().isOk())
                .andExpect(
                        content()
                                .json(
                                        "[{\"name\":\"System\",\"displayName\":\"System\",\"day\":\"2020-10-30\",\"weatherAttributes\":{\"precip\":2.0,\"windGustMax\":4.5,\"windSustained\":6.0,\"temperatureMin\":20.0,\"temperatureMean\":30.0,\"temperatureMax\":40.0,\"threatLevel\":9.0,\"threatLevelCategory\":\"Threat\",\"cloudCoverPercentMean\":98.9}}]"));
    }

    @WithMockUser
    @Test
    void testGetLatestSessionByTypeWhereSessionIdIsEmpty() throws Exception {
        when(regionalWeatherService.getLatestSession(
                        LatestSessionRequestDto.builder().type("daily_zone").build()))
                .thenThrow(DomainObjectNotFoundException.class);

        mvc.perform(get("/weatherAttributes/sessions/latest").param("type", "daily_zone"))
                .andExpect(status().isNotFound());
    }
}

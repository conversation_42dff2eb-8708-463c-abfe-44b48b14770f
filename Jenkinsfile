pipeline {
  agent {
    kubernetes {
      inheritFrom 'default-agent'
      yaml """
      apiVersion: v1
      kind: Pod
      spec:
        containers:
        - name: maven
          image: maven:3.9-amazoncorretto-21
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
          volumeMounts:
            - mountPath: /var/maven/jfrog_maven_settings.xml
              subPath: jfrog_maven_settings.xml
              name: jfrog-maven-settings
              readOnly: true
        - name: docker-client
          image: docker:24
          command: ['sleep', '99d']
          env:
            - name: DOCKER_HOST
              value: tcp://localhost:2375
        - name: snyk
          image: snyk/snyk:maven
          command: ['sleep', '99d']
        - name: aws-cli
          image: amazon/aws-cli:latest
          command: ['sleep', '99d']
        - name: docker-daemon
          image: docker:24-dind
          env:
            - name: DOCKER_TLS_CERTDIR
              value: ""
          securityContext:
            privileged: true
          volumeMounts:
              - name: cache
                mountPath: /var/lib/docker
        volumes:
          - name: cache
            emptyDir: {}
          - name: jfrog-maven-settings
            secret:
              secretName: jfrog-maven-settings
      """
    }
  }
  triggers {
    // Run the pipeline on a schedule for fresh image scanning -> every 1st and 15th of the month at 12:01 AM
    cron((env.BRANCH_NAME in ["development", "develop"]) ? "1 0 1,15 * *" : "")
  }
  environment {
    ECR_REPO = '411985166407.dkr.ecr.us-east-1.amazonaws.com'
    OFFICE365_WEBHOOK = ''
    SUCCESS = "#28A745"
    WARNING = "#FFC107"
    FAILURE = "#DC3545"
  }
  parameters {
    string(name: 'IMAGE_NAME', defaultValue: 'esource/storm-weather-api')
    string(name: 'VERSION',    defaultValue: 'latest')
  }
  stages {
    stage('Prepare Environment') {
      steps {
        script {
          env.TIMER_TRIGGERED = 'false' // Default value
          // Get the list of causes for the current build
          def causes = currentBuild.getBuildCauses()

          // Iterate over each cause and check if it's a timer trigger
          for (cause in causes) {
              echo "Build cause: ${cause}"
              if (cause.shortDescription == 'Started by timer') {
                  // If the cause is 'Started by timer', set the environment variable
                  env.TIMER_TRIGGERED = 'true'
              }
          }

          echo "TIMER_TRIGGERED: ${env.TIMER_TRIGGERED}"
        }
      }
    }
    stage('Create Canonical Version') {
      steps {
        script {
          escaped_branch = sh(script: 'echo $GIT_BRANCH | sed "s=/=_=g"', returnStdout: true).trim()
          timestamp = sh(script: 'date +%s', returnStdout: true).trim()
          // grab latest security scan
          if (env.TIMER_TRIGGERED == 'true') {
            escaped_branch = 'scan'
          }
          env.VERSION = "${escaped_branch}-${GIT_COMMIT[0..7]}-${timestamp}"
        }
      }
    }
    stage('Build JAR') {
      steps {
        container('aws-cli') {
          // Obtain the AWS CodeArtifact authorization token
          script {
            env.CODEARTIFACT_AUTH_TOKEN = sh(script: "aws codeartifact get-authorization-token --domain esource-int-artifacts --domain-owner 411985166407 --query authorizationToken --output text", returnStdout: true).trim()
          }
        }
        container('maven') {
          // Dynamically create the settings.xml using the obtained token
          sh """
            mkdir -p /root/.m2
            echo '<?xml version="1.0" encoding="UTF-8"?>
            <settings xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.2.0 http://maven.apache.org/xsd/settings-1.2.0.xsd" xmlns="http://maven.apache.org/SETTINGS/1.2.0"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
              <servers>
                <server>
                  <id>esource-int-artifacts-maven</id>
                  <username>aws</username>
                  <password>'"${env.CODEARTIFACT_AUTH_TOKEN}"'</password>
                </server>
              </servers>
              <profiles>
                <profile>
                  <id>maven-profile</id>
                  <repositories>
                    <repository>
                      <id>maven-central</id>
                      <name>Maven Central Repository</name>
                      <url>https://repo1.maven.org/maven2</url>
                      <layout>default</layout>
                      <snapshots>
                        <enabled>false</enabled>
                      </snapshots>
                    </repository>
                    <repository>
                      <id>esource-int-artifacts-maven</id>
                      <url>https://esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/maven/maven/</url>
                    </repository>
                  </repositories>
                  <pluginRepositories>
                    <pluginRepository>
                      <id>maven-central</id>
                      <name>Maven Central Repository</name>
                      <url>https://repo1.maven.org/maven2</url>
                      <layout>default</layout>
                    </pluginRepository>
                    <pluginRepository>
                      <id>esource-int-artifacts-maven</id>
                      <url>https://esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/maven/maven/</url>
                    </pluginRepository>
                  </pluginRepositories>
                </profile>
              </profiles>
              <activeProfiles>
                <activeProfile>maven-profile</activeProfile>
              </activeProfiles>
            </settings>' > /root/.m2/settings.xml
          """
          sh "mvn -s /root/.m2/settings.xml -U clean compile"
        }
      }
    }
    stage('Test') {
      when {
        anyOf {
          // we just want a fresh image for our security scans
          expression {return env.TIMER_TRIGGERED == 'false'}
        }
      }
       steps {
         container('maven') {
             sh 'mvn -s /var/maven/jfrog_maven_settings.xml -U test -Dspring.main.cloud-platform=NONE -Dmaven.test.failure.ignore=false -Djacoco-maven-plugin.version=0.8.11'
         }
       }
       post {
         always {
           junit '**/target/surefire-reports/*.xml'
           jacoco()
         }
       }
     }
    stage('Quality & Security') {
      when {
        anyOf {
          // we just want a fresh image for our security scans
          expression {return env.TIMER_TRIGGERED == 'false'}
        }
      }
      parallel {
//         stage('Sonar Code Quality Scan') {
//           tools {
//             'hudson.plugins.sonar.SonarRunnerInstallation' 'sonar'
//           }
//           steps {
//             container('maven') {
//               withSonarQubeEnv('SonarQube Community Server') {
//                 sh 'mvn org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar -Dsonar.qualitygate.wait=true'
//               }
//             }
//           }
//         }
        stage('Snyk Security Scan') {
          steps {
            container('snyk') {
              withCredentials([string(credentialsId: 'snyk_token', variable: 'SNYK_TOKEN')]) {
                sh 'snyk test --severity-threshold=high --fail-on=all'
              }
            }
          }
        }
      }
    }
    stage('Build Docker Image') {
      steps {
        container('maven') {
          sh "mvn -s /var/maven/jfrog_maven_settings.xml install -DskipTests"
          sh "mvn -s /var/maven/jfrog_maven_settings.xml spring-boot:build-image -Ddocker.image.version=${env.VERSION} -DskipTests"
        }
      }
    }
    stage('Push Docker Image to AWS ECR') {
      when {
        anyOf {
          branch 'main'
          branch 'staging'
          branch 'development'
          expression{ return env.TIMER_TRIGGERED == 'true'}
        }
      }
      steps {
        script {
          docker_pwd = ''
          imageName = "${env.IMAGE_NAME}:${env.VERSION}"
          repository = '411985166407.dkr.ecr.us-east-1.amazonaws.com'
          container('aws-cli') {
            docker_pwd = sh(script: 'aws ecr get-login-password --region us-east-1', returnStdout: true)
          }
          container('docker-client') {
            sh "echo \"${docker_pwd}\" | docker login -u AWS --password-stdin ${repository}"
            sh "docker tag ${imageName} ${repository}/${imageName}"
            sh "docker push ${repository}/${imageName}"
          }
        }
      }
      post {
        success {
          script {
            if (!(env.BRANCH_NAME in ['main', 'staging'])) {
              echo "Not in main or staging, will not send notification"
              return
            }
            office365ConnectorSend webhookUrl: "${env.OFFICE365_WEBHOOK}",
            status: 'Build completed successfully.',
            message: "Build '${env.JOB_NAME}#${env.BUILD_NUMBER}' (${env.BUILD_URL}) completed successfully.",
            facts: [
              [name: 'Branch', template: env.GIT_BRANCH],
              [name: 'Commit', template: env.GIT_COMMIT],
              [name: 'Image', template: "${env.ECR_REPO}/${env.IMAGE_NAME}:${env.VERSION}"]
            ],
            color: env.SUCCESS
          }
        }
        failure {
          script {
            if (!(env.BRANCH_NAME in ['main', 'staging'])) {
              echo "Not in main or staging, will not send notification"
              return
            }
            office365ConnectorSend webhookUrl: "${env.OFFICE365_WEBHOOK}",
            status: 'Build failed.',
            message: "Build '${env.JOB_NAME}#${env.BUILD_NUMBER}' (${env.BUILD_URL}) failed.",
            factDefinitions: [
              [name: 'Branch', template: env.GIT_BRANCH],
              [name: 'Commit', template: env.GIT_COMMIT]
            ],
            color: env.FAILURE
          }
        }
      }
    }
    stage('Deploy to APC Development Azure CR') {
      when {
        anyOf {
          branch 'development'
        }
      }
      steps {
        container('docker-client') {
          script {
            repository_a = "aprampdvacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${repository_a}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_a}/$IMAGE_NAME:$VERSION"
            sh "docker push ${repository_a}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_a}/$IMAGE_NAME:latest"
            sh "docker push ${repository_a}/$IMAGE_NAME:latest"
          }
        }
      }
    }
    stage('Deploy to APC UA Azure CR') {
      when {
        branch 'staging'
      }
      steps {
        container('docker-client') {
          script {
            repository_b = "aprampuaacr.azurecr.io"
            withCredentials([usernamePassword(credentialsId: 'apc_ua_azurecr_credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              sh "docker login -u $DOCKER_USERNAME -p $DOCKER_PASSWORD ${repository_b}"
            }
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_b}/$IMAGE_NAME:$VERSION"
            sh "docker push ${repository_b}/$IMAGE_NAME:$VERSION"
            sh "docker tag $IMAGE_NAME:$VERSION ${repository_b}/$IMAGE_NAME:latest"
            sh "docker push ${repository_b}/$IMAGE_NAME:latest"
          }
        }
      }
    }
  }
}
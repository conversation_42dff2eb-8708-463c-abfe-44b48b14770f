<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.7</version>
		<relativePath/>
		<!-- lookup parent from repository -->
	</parent>
	<groupId>com.esource</groupId>
	<artifactId>storm-weather-api</artifactId>
	<version>1.0.2</version>
	<name>storm-weather-api</name>
	<description>Storm Weather Service</description>
	<properties>
		<java.version>21</java.version>
		<docker.image.repo>411985166407.dkr.ecr.us-east-1.amazonaws.com/esource/storm-weather-api</docker.image.repo>
		<docker.image.tag>latest</docker.image.tag>
		<testcontainers.version>1.20.4</testcontainers.version>
		<sonar.projectKey>trovedata_storm-weather-api_33ed6904-625a-4d24-aaf0-7dbf5e69253a</sonar.projectKey>
		<sonar.qualitygate.wait>true</sonar.qualitygate.wait>
		<spotless.version>2.43.0</spotless.version>
		<mapstruct.version>1.6.3</mapstruct.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-rest</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>com.esource.security</groupId>
			<artifactId>security-adapter</artifactId>
			<version>3.3.5</version>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>mongodb</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.8.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.18.0</version>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${mapstruct.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
			<version>${mapstruct.version}</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>2024.0.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.testcontainers</groupId>
				<artifactId>testcontainers-bom</artifactId>
				<version>${testcontainers.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>Build Docker Image</id>
						<phase>install</phase>
						<goals>
							<goal>build-image</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<layers>
						<enabled>true</enabled>
					</layers>
					<image>
						<name>${docker.image.repo}:${docker.image.tag}</name>
						<builder>paketobuildpacks/builder-jammy-tiny:latest</builder>
						<createdDate>now</createdDate>
					</image>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>

					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>							<!-- IMPORTANT - LOMBOK BEFORE MAPSTRUCT -->
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${mapstruct.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.11</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>${maven-surefire-plugin.version}</version>
				<configuration>
					<trimStackTrace>false</trimStackTrace>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.diffplug.spotless</groupId>
				<artifactId>spotless-maven-plugin</artifactId>
				<version>${spotless.version}</version>
				<configuration>
					<!-- optional: limit format enforcement to just the files changed by this feature branch-->
					<!-- <ratchetFrom>origin/main</ratchetFrom> -->
					<formats>
						<format>
							<includes>
								<include>.gitattributes</include>
								<include>.gitignore</include>
							</includes>
							<trimTrailingWhitespace/>
							<endWithNewline/>
							<indent>
								<spaces>true</spaces>
								<spacesPerTab>4</spacesPerTab>
							</indent>
						</format>
					</formats>
					<java>
						<palantirJavaFormat />
						<formatAnnotations />
						<removeUnusedImports />
						<trimTrailingWhitespace />
						<endWithNewline />
						<indent>
							<spaces>true</spaces>
							<spacesPerTab>4</spacesPerTab>
						</indent>
						<removeUnusedImports>
							<engine>google-java-format</engine>
						</removeUnusedImports>
						<importOrder>
							<order>java|javax,org,com,com.diffplug,,\#com.diffplug,\#</order>
							<semanticSort>false</semanticSort>
						</importOrder>
					</java>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>apply</goal>
						</goals>
						<phase>compile</phase>
					</execution>
				</executions>
			</plugin>

		</plugins>
	</build>

	<distributionManagement>
		<repository>
			<id>esource-int-artifacts-maven</id>
			<name>esource-int-artifacts-maven</name>
			<url>https://esource-int-artifacts-411985166407.d.codeartifact.us-east-1.amazonaws.com/maven/maven/</url>
		</repository>
	</distributionManagement>

</project>
